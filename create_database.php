<?php
/**
 * إنشاء قاعدة البيانات تلقائياً
 * Automatic Database Creation
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>إنشاء قاعدة البيانات</h1>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "<p style='color: green;'>✓ تم الاتصال بـ MySQL بنجاح</p>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS dawah_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✓ تم إنشاء قاعدة البيانات dawah_management</p>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $pdo->exec("USE dawah_management");
    echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات</p>";
    
    // قراءة وتنفيذ ملف SQL
    $sqlFile = 'sql/database.sql';
    if (file_exists($sqlFile)) {
        $sql = file_get_contents($sqlFile);
        
        // تقسيم الاستعلامات
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // تجاهل أخطاء الجداول الموجودة
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "<p style='color: orange;'>تحذير: " . $e->getMessage() . "</p>";
                    }
                }
            }
        }
        
        echo "<p style='color: green;'>✓ تم تنفيذ ملف SQL بنجاح</p>";
    } else {
        echo "<p style='color: red;'>✗ ملف sql/database.sql غير موجود</p>";
    }
    
    // التحقق من الجداول
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h2>الجداول المنشأة:</h2>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li style='color: green;'>✓ $table</li>";
    }
    echo "</ul>";
    
    echo "<h2>✅ تم إعداد قاعدة البيانات بنجاح!</h2>";
    echo "<p><a href='simple_test.php'>اختبار النظام الآن</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ: " . $e->getMessage() . "</p>";
    echo "<h3>تحقق من:</h3>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL في WAMP</li>";
    echo "<li>صحة اسم المستخدم وكلمة المرور</li>";
    echo "<li>صلاحيات إنشاء قواعد البيانات</li>";
    echo "</ul>";
}
?>
