<?php
/**
 * متحكم إدارة المستخدمين
 * User Management Controller
 */

require_once ROOT_PATH . '/models/User.php';
require_once ROOT_PATH . '/models/Preacher.php';

class UserController {
    private $userModel;
    private $preacherModel;
    
    public function __construct() {
        $this->userModel = new User();
        $this->preacherModel = new Preacher();
    }
    
    /**
     * عرض قائمة المستخدمين
     */
    public function index() {
        requireAdmin();
        
        $users = $this->userModel->getAll();
        
        // جلب معلومات الدعاة المرتبطين
        foreach ($users as &$user) {
            if ($user['role'] === 'preacher') {
                $preacher = $this->preacherModel->getByUserId($user['id']);
                $user['preacher_info'] = $preacher;
            }
        }
        
        include ROOT_PATH . '/views/admin/users/index.php';
    }
    
    /**
     * عرض صفحة إضافة مستخدم جديد
     */
    public function create() {
        requireAdmin();
        
        include ROOT_PATH . '/views/admin/users/create.php';
    }
    
    /**
     * معالجة إضافة مستخدم جديد
     */
    public function store() {
        requireAdmin();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(APP_URL . '/index.php?page=users');
        }
        
        // التحقق من رمز CSRF
        if (!validateCSRF($_POST['csrf_token'] ?? '')) {
            showMessage('رمز الأمان غير صحيح', 'error');
            redirect(APP_URL . '/index.php?page=users&action=create');
        }
        
        $username = sanitize($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        $fullName = sanitize($_POST['full_name'] ?? '');
        $email = sanitize($_POST['email'] ?? '');
        $role = sanitize($_POST['role'] ?? 'preacher');
        
        // بيانات الداعية (إذا كان المستخدم داعية)
        $preacherData = [];
        if ($role === 'preacher') {
            $preacherData = [
                'phone' => sanitize($_POST['phone'] ?? ''),
                'address' => sanitize($_POST['address'] ?? ''),
                'specialization' => sanitize($_POST['specialization'] ?? ''),
                'education' => sanitize($_POST['education'] ?? ''),
                'experience_years' => intval($_POST['experience_years'] ?? 0),
                'languages' => sanitize($_POST['languages'] ?? ''),
                'bio' => sanitize($_POST['bio'] ?? '')
            ];
        }
        
        // التحقق من صحة البيانات
        $errors = [];
        
        if (empty($username)) {
            $errors[] = 'اسم المستخدم مطلوب';
        } elseif ($this->userModel->usernameExists($username)) {
            $errors[] = 'اسم المستخدم موجود مسبقاً';
        }
        
        if (empty($password)) {
            $errors[] = 'كلمة المرور مطلوبة';
        } elseif (strlen($password) < 6) {
            $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
        
        if ($password !== $confirmPassword) {
            $errors[] = 'كلمة المرور وتأكيدها غير متطابقتين';
        }
        
        if (empty($fullName)) {
            $errors[] = 'الاسم الكامل مطلوب';
        }
        
        if (!empty($email) && !validateEmail($email)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        if (!in_array($role, ['admin', 'preacher'])) {
            $errors[] = 'نوع المستخدم غير صحيح';
        }
        
        if (!empty($errors)) {
            showMessage(implode('<br>', $errors), 'error');
            redirect(APP_URL . '/index.php?page=users&action=create');
        }
        
        // إنشاء المستخدم
        $userData = [
            'username' => $username,
            'password' => $password,
            'full_name' => $fullName,
            'email' => $email,
            'role' => $role
        ];
        
        $userId = $this->userModel->createAndGetId($userData);
        
        if ($userId) {
            // إذا كان المستخدم داعية، إنشاء سجل الداعية
            if ($role === 'preacher') {
                $preacherData['user_id'] = $userId;
                $preacherData['full_name'] = $fullName;
                $preacherData['email'] = $email;
                
                if (!$this->preacherModel->create($preacherData)) {
                    showMessage('تم إنشاء المستخدم ولكن فشل في إنشاء ملف الداعية', 'warning');
                }
            }
            
            showMessage('تم إنشاء المستخدم بنجاح', 'success');
            redirect(APP_URL . '/index.php?page=users');
        } else {
            showMessage('حدث خطأ أثناء إنشاء المستخدم', 'error');
            redirect(APP_URL . '/index.php?page=users&action=create');
        }
    }
    
    /**
     * عرض صفحة تعديل المستخدم
     */
    public function edit() {
        requireAdmin();
        
        $id = intval($_GET['id'] ?? 0);
        $user = $this->userModel->getById($id);
        
        if (!$user) {
            showMessage('المستخدم غير موجود', 'error');
            redirect(APP_URL . '/index.php?page=users');
        }
        
        // جلب بيانات الداعية إذا كان المستخدم داعية
        $preacher = null;
        if ($user['role'] === 'preacher') {
            $preacher = $this->preacherModel->getByUserId($id);
        }
        
        include ROOT_PATH . '/views/admin/users/edit.php';
    }
    
    /**
     * معالجة تحديث المستخدم
     */
    public function update() {
        requireAdmin();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(APP_URL . '/index.php?page=users');
        }
        
        $id = intval($_POST['id'] ?? 0);
        $user = $this->userModel->getById($id);
        
        if (!$user) {
            showMessage('المستخدم غير موجود', 'error');
            redirect(APP_URL . '/index.php?page=users');
        }
        
        // التحقق من رمز CSRF
        if (!validateCSRF($_POST['csrf_token'] ?? '')) {
            showMessage('رمز الأمان غير صحيح', 'error');
            redirect(APP_URL . '/index.php?page=users&action=edit&id=' . $id);
        }
        
        $fullName = sanitize($_POST['full_name'] ?? '');
        $email = sanitize($_POST['email'] ?? '');
        $role = sanitize($_POST['role'] ?? 'preacher');
        $password = $_POST['password'] ?? '';
        
        // التحقق من صحة البيانات
        $errors = [];
        
        if (empty($fullName)) {
            $errors[] = 'الاسم الكامل مطلوب';
        }
        
        if (!empty($email) && !validateEmail($email)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        if (!in_array($role, ['admin', 'preacher'])) {
            $errors[] = 'نوع المستخدم غير صحيح';
        }
        
        if (!empty($password) && strlen($password) < 6) {
            $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
        
        if (!empty($errors)) {
            showMessage(implode('<br>', $errors), 'error');
            redirect(APP_URL . '/index.php?page=users&action=edit&id=' . $id);
        }
        
        // تحديث بيانات المستخدم
        $updateData = [
            'full_name' => $fullName,
            'email' => $email,
            'role' => $role
        ];
        
        if (!empty($password)) {
            $updateData['password'] = $password;
        }
        
        if ($this->userModel->update($id, $updateData)) {
            // تحديث بيانات الداعية إذا كان المستخدم داعية
            if ($role === 'preacher') {
                $preacherData = [
                    'full_name' => $fullName,
                    'email' => $email,
                    'phone' => sanitize($_POST['phone'] ?? ''),
                    'address' => sanitize($_POST['address'] ?? ''),
                    'specialization' => sanitize($_POST['specialization'] ?? ''),
                    'education' => sanitize($_POST['education'] ?? ''),
                    'experience_years' => intval($_POST['experience_years'] ?? 0),
                    'languages' => sanitize($_POST['languages'] ?? ''),
                    'bio' => sanitize($_POST['bio'] ?? '')
                ];
                
                $existingPreacher = $this->preacherModel->getByUserId($id);
                if ($existingPreacher) {
                    $this->preacherModel->update($existingPreacher['id'], $preacherData);
                } else {
                    $preacherData['user_id'] = $id;
                    $this->preacherModel->create($preacherData);
                }
            }
            
            showMessage('تم تحديث المستخدم بنجاح', 'success');
        } else {
            showMessage('حدث خطأ أثناء تحديث المستخدم', 'error');
        }
        
        redirect(APP_URL . '/index.php?page=users');
    }
    
    /**
     * حذف المستخدم
     */
    public function delete() {
        requireAdmin();
        
        $id = intval($_GET['id'] ?? 0);
        $user = $this->userModel->getById($id);
        
        if (!$user) {
            showMessage('المستخدم غير موجود', 'error');
            redirect(APP_URL . '/index.php?page=users');
        }
        
        // منع حذف المستخدم الحالي
        if ($id == $_SESSION['user_id']) {
            showMessage('لا يمكنك حذف حسابك الخاص', 'error');
            redirect(APP_URL . '/index.php?page=users');
        }
        
        // حذف بيانات الداعية أولاً إذا كان موجوداً
        if ($user['role'] === 'preacher') {
            $preacher = $this->preacherModel->getByUserId($id);
            if ($preacher) {
                $this->preacherModel->delete($preacher['id']);
            }
        }
        
        // حذف المستخدم
        if ($this->userModel->delete($id)) {
            showMessage('تم حذف المستخدم بنجاح', 'success');
        } else {
            showMessage('حدث خطأ أثناء حذف المستخدم', 'error');
        }
        
        redirect(APP_URL . '/index.php?page=users');
    }
    
    /**
     * تفعيل/إلغاء تفعيل المستخدم
     */
    public function toggleActive() {
        requireAdmin();
        
        $id = intval($_GET['id'] ?? 0);
        $user = $this->userModel->getById($id);
        
        if (!$user) {
            showMessage('المستخدم غير موجود', 'error');
            redirect(APP_URL . '/index.php?page=users');
        }
        
        // منع تعطيل المستخدم الحالي
        if ($id == $_SESSION['user_id']) {
            showMessage('لا يمكنك تعطيل حسابك الخاص', 'error');
            redirect(APP_URL . '/index.php?page=users');
        }
        
        if ($this->userModel->toggleActive($id)) {
            $status = $user['is_active'] ? 'تعطيل' : 'تفعيل';
            showMessage("تم $status المستخدم بنجاح", 'success');
        } else {
            showMessage('حدث خطأ أثناء تغيير حالة المستخدم', 'error');
        }
        
        redirect(APP_URL . '/index.php?page=users');
    }
}
?>
