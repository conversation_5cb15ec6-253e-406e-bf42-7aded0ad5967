<?php
/**
 * صفحة التسجيل
 * Registration Page
 */

// التحقق من عدم تسجيل الدخول مسبقاً
if (isLoggedIn()) {
    redirect(APP_URL . '/index.php?page=dashboard');
}

$error = '';
$success = '';

// معالجة نموذج التسجيل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $full_name = trim($_POST['full_name'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    // التحقق من صحة البيانات
    if (empty($username) || empty($full_name) || empty($password)) {
        $error = 'اسم المستخدم والاسم الكامل وكلمة المرور مطلوبة';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمات المرور غير متطابقة';
    } elseif (strlen($password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } else {
        // محاولة إنشاء المستخدم
        try {
            $userModel = new User();

            // التحقق من عدم وجود اسم المستخدم
            if ($userModel->usernameExists($username)) {
                $error = 'اسم المستخدم موجود مسبقاً';
            } else {
                $userData = [
                    'username' => $username,
                    'password' => $password,
                    'full_name' => $full_name,
                    'email' => $email,
                    'role' => 'admin' // أول مستخدم يكون مشرف
                ];

                if ($userModel->create($userData)) {
                    $success = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.';
                } else {
                    $error = 'حدث خطأ أثناء إنشاء الحساب';
                }
            }
        } catch (Exception $e) {
            $error = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التسجيل - <?php echo APP_NAME; ?></title>
    <link href="<?php echo APP_URL; ?>/assets/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="<?php echo APP_URL; ?>/assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-sm mt-5">
                    <div class="card-header bg-primary text-white text-center">
                        <h4 class="mb-0">إنشاء حساب جديد</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
                        <?php endif; ?>

                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم *</label>
                                <input type="text" class="form-control" id="username" name="username"
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name"
                                       value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" required>
                            </div>



                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور *</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                            </div>

                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">إنشاء الحساب</button>
                            </div>
                        </form>

                        <div class="text-center mt-3">
                            <p>لديك حساب بالفعل؟ <a href="<?php echo APP_URL; ?>/index.php?page=login">تسجيل الدخول</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="<?php echo APP_URL; ?>/assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>
