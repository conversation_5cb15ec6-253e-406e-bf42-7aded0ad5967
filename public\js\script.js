/**
 * ملف JavaScript المخصص لتطبيق إدارة شؤون الدعاة
 * Custom JavaScript for Da'wah Management System
 */

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // تهيئة التأثيرات البصرية
    initializeAnimations();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة الأزرار
    initializeButtons();
    
    // تهيئة رفع الملفات
    initializeFileUploads();
    
    // تهيئة البحث
    initializeSearch();
    
    // تهيئة التنبيهات
    initializeAlerts();
    
    console.log('تم تهيئة التطبيق بنجاح');
}

/**
 * تهيئة التأثيرات البصرية
 */
function initializeAnimations() {
    // إضافة تأثير الظهور التدريجي للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
    
    // تأثيرات التمرير للبطاقات
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
        });
    });
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        // التحقق من صحة النماذج
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                showNotification('يرجى ملء جميع الحقول المطلوبة بشكل صحيح', 'error');
            }
        });
        
        // تحسين تجربة المستخدم للحقول
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            // إزالة رسائل الخطأ عند الكتابة
            input.addEventListener('input', function() {
                this.classList.remove('is-invalid');
                const feedback = this.parentNode.querySelector('.invalid-feedback');
                if (feedback) {
                    feedback.remove();
                }
            });
            
            // تأثيرات بصرية للتركيز
            input.addEventListener('focus', function() {
                this.parentNode.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                this.parentNode.classList.remove('focused');
                validateField(this);
            });
        });
    });
}

/**
 * التحقق من صحة النموذج
 */
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * التحقق من صحة الحقل
 */
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';
    
    // التحقق من الحقول المطلوبة
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        message = 'هذا الحقل مطلوب';
    }
    
    // التحقق من البريد الإلكتروني
    if (field.type === 'email' && value && !isValidEmail(value)) {
        isValid = false;
        message = 'البريد الإلكتروني غير صحيح';
    }
    
    // التحقق من رقم الهاتف
    if (field.type === 'tel' && value && !isValidPhone(value)) {
        isValid = false;
        message = 'رقم الهاتف غير صحيح';
    }
    
    // التحقق من كلمة المرور
    if (field.type === 'password' && value && value.length < 6) {
        isValid = false;
        message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    
    // عرض رسالة الخطأ
    if (!isValid) {
        field.classList.add('is-invalid');
        showFieldError(field, message);
    } else {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        removeFieldError(field);
    }
    
    return isValid;
}

/**
 * عرض رسالة خطأ للحقل
 */
function showFieldError(field, message) {
    removeFieldError(field);
    
    const feedback = document.createElement('div');
    feedback.className = 'invalid-feedback';
    feedback.textContent = message;
    
    field.parentNode.appendChild(feedback);
}

/**
 * إزالة رسالة خطأ الحقل
 */
function removeFieldError(field) {
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.remove();
    }
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * التحقق من صحة رقم الهاتف
 */
function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[0-9\-\(\)\s]+$/;
    return phoneRegex.test(phone) && phone.length >= 10;
}

/**
 * تهيئة الجداول
 */
function initializeTables() {
    const tables = document.querySelectorAll('.table');
    
    tables.forEach(table => {
        // تأثيرات التمرير للصفوف
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#f8f9fa';
                this.style.transform = 'scale(1.01)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
                this.style.transform = 'scale(1)';
            });
        });
        
        // إضافة ترقيم للصفوف
        addRowNumbers(table);
    });
}

/**
 * إضافة ترقيم للصفوف
 */
function addRowNumbers(table) {
    const rows = table.querySelectorAll('tbody tr');
    const hasNumberColumn = table.querySelector('th:first-child').textContent.includes('#');
    
    if (!hasNumberColumn) {
        rows.forEach((row, index) => {
            const numberCell = document.createElement('td');
            numberCell.textContent = index + 1;
            numberCell.className = 'text-muted small';
            row.insertBefore(numberCell, row.firstChild);
        });
        
        // إضافة عنوان العمود
        const headerRow = table.querySelector('thead tr');
        if (headerRow) {
            const numberHeader = document.createElement('th');
            numberHeader.textContent = '#';
            numberHeader.style.width = '50px';
            headerRow.insertBefore(numberHeader, headerRow.firstChild);
        }
    }
}

/**
 * تهيئة الأزرار
 */
function initializeButtons() {
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        // تأثير النقر
        button.addEventListener('click', function(e) {
            // تأثير الموجة
            createRippleEffect(this, e);
            
            // تأثير التصغير
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 100);
        });
        
        // تأثير التمرير
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

/**
 * إنشاء تأثير الموجة
 */
function createRippleEffect(button, event) {
    const ripple = document.createElement('span');
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');
    
    button.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * تهيئة رفع الملفات
 */
function initializeFileUploads() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            handleFileUpload(this);
        });
        
        // إضافة منطقة السحب والإفلات
        createDropZone(input);
    });
}

/**
 * معالجة رفع الملف
 */
function handleFileUpload(input) {
    const file = input.files[0];
    if (!file) return;
    
    // التحقق من حجم الملف (5MB)
    if (file.size > 5 * 1024 * 1024) {
        showNotification('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت', 'error');
        input.value = '';
        return;
    }
    
    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (input.accept && !allowedTypes.includes(file.type)) {
        showNotification('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)', 'error');
        input.value = '';
        return;
    }
    
    // عرض معاينة الصورة
    if (file.type.startsWith('image/')) {
        showImagePreview(input, file);
    }
    
    showNotification('تم اختيار الملف بنجاح', 'success');
}

/**
 * عرض معاينة الصورة
 */
function showImagePreview(input, file) {
    const reader = new FileReader();
    
    reader.onload = function(e) {
        let preview = document.getElementById('image-preview');
        if (!preview) {
            preview = document.createElement('img');
            preview.id = 'image-preview';
            preview.className = 'img-thumbnail mt-2';
            preview.style.maxWidth = '200px';
            preview.style.maxHeight = '200px';
            input.parentNode.appendChild(preview);
        }
        preview.src = e.target.result;
        preview.style.display = 'block';
    };
    
    reader.readAsDataURL(file);
}

/**
 * إنشاء منطقة السحب والإفلات
 */
function createDropZone(input) {
    const dropZone = document.createElement('div');
    dropZone.className = 'drop-zone';
    dropZone.innerHTML = `
        <i class="fas fa-cloud-upload-alt fa-2x mb-2"></i>
        <p>اسحب الملف هنا أو انقر للاختيار</p>
    `;
    
    // إدراج منطقة السحب
    input.style.display = 'none';
    input.parentNode.insertBefore(dropZone, input);
    
    // أحداث السحب والإفلات
    dropZone.addEventListener('click', () => input.click());
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('drop', (e) => handleDrop(e, input));
}

/**
 * معالجة السحب فوق المنطقة
 */
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('drag-over');
}

/**
 * معالجة الإفلات
 */
function handleDrop(e, input) {
    e.preventDefault();
    e.currentTarget.classList.remove('drag-over');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        input.files = files;
        handleFileUpload(input);
    }
}

/**
 * تهيئة البحث
 */
function initializeSearch() {
    const searchInputs = document.querySelectorAll('input[type="search"], input[name="search"]');
    
    searchInputs.forEach(input => {
        let timeout;
        
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                performSearch(this);
            }, 500);
        });
        
        // إضافة أيقونة البحث
        addSearchIcon(input);
    });
}

/**
 * تنفيذ البحث
 */
function performSearch(input) {
    const query = input.value.trim();
    
    if (query.length < 2) return;
    
    // يمكن إضافة البحث المباشر هنا
    console.log('البحث عن:', query);
}

/**
 * إضافة أيقونة البحث
 */
function addSearchIcon(input) {
    if (input.parentNode.classList.contains('input-group')) return;
    
    const wrapper = document.createElement('div');
    wrapper.className = 'input-group';
    
    input.parentNode.insertBefore(wrapper, input);
    wrapper.appendChild(input);
    
    const icon = document.createElement('span');
    icon.className = 'input-group-text';
    icon.innerHTML = '<i class="fas fa-search"></i>';
    wrapper.appendChild(icon);
}

/**
 * تهيئة التنبيهات
 */
function initializeAlerts() {
    // إخفاء التنبيهات تلقائياً
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.classList.contains('alert-dismissible')) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        });
    }, 5000);
}

/**
 * عرض إشعار
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار تلقائياً
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * تأكيد الحذف
 */
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

/**
 * تأكيد تغيير الحالة
 */
function confirmToggle(message = 'هل أنت متأكد من تغيير الحالة؟') {
    return confirm(message);
}

/**
 * تنسيق الأرقام
 */
function formatNumber(num) {
    return new Intl.NumberFormat('ar-SA').format(num);
}

/**
 * تنسيق التاريخ
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

/**
 * طباعة الصفحة
 */
function printPage() {
    window.print();
}

/**
 * تصدير البيانات
 */
function exportData(format, url) {
    window.location.href = url + '&format=' + format;
}

/**
 * تحديث الصفحة
 */
function refreshPage() {
    location.reload();
}

/**
 * العودة للصفحة السابقة
 */
function goBack() {
    history.back();
}

// إضافة أنماط CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .drop-zone {
        border: 2px dashed #ccc;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }
    
    .drop-zone:hover,
    .drop-zone.drag-over {
        border-color: #007bff;
        background: #e3f2fd;
    }
    
    .focused {
        transform: scale(1.02);
    }
`;
document.head.appendChild(style);
