<?php
/**
 * نموذج المستخدم
 * User Model
 */

class User {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * تسجيل دخول المستخدم
     */
    public function login($username, $password) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, username, password, full_name, email, role, is_active
                FROM users
                WHERE username = ? AND is_active = 1
            ");
            $stmt->execute([$username]);
            $user = $stmt->fetch();

            if ($user && verifyPassword($password, $user['password'])) {
                return $user;
            }

            return false;
        } catch (PDOException $e) {
            error_log("خطأ في تسجيل الدخول: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إنشاء مستخدم جديد
     */
    public function create($data) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO users (username, password, full_name, email, role)
                VALUES (?, ?, ?, ?, ?)
            ");

            $hashedPassword = hashPassword($data['password']);

            return $stmt->execute([
                $data['username'],
                $hashedPassword,
                $data['full_name'],
                $data['email'],
                $data['role'] ?? 'preacher'
            ]);
        } catch (PDOException $e) {
            error_log("خطأ في إنشاء المستخدم: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على مستخدم بواسطة المعرف
     */
    public function getById($id) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, username, full_name, email, role, is_active, created_at
                FROM users
                WHERE id = ?
            ");
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("خطأ في جلب المستخدم: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على جميع المستخدمين
     */
    public function getAll() {
        try {
            $stmt = $this->db->prepare("
                SELECT id, username, full_name, email, role, is_active, created_at
                FROM users
                ORDER BY created_at DESC
            ");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في جلب المستخدمين: " . $e->getMessage());
            return [];
        }
    }

    /**
     * تحديث بيانات المستخدم
     */
    public function update($id, $data) {
        try {
            $sql = "UPDATE users SET full_name = ?, email = ?, role = ?";
            $params = [$data['full_name'], $data['email'], $data['role']];

            // تحديث كلمة المرور إذا تم توفيرها
            if (!empty($data['password'])) {
                $sql .= ", password = ?";
                $params[] = hashPassword($data['password']);
            }

            $sql .= " WHERE id = ?";
            $params[] = $id;

            $stmt = $this->db->prepare($sql);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("خطأ في تحديث المستخدم: " . $e->getMessage());
            return false;
        }
    }

    /**
     * حذف مستخدم
     */
    public function delete($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM users WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("خطأ في حذف المستخدم: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تفعيل/إلغاء تفعيل المستخدم
     */
    public function toggleActive($id) {
        try {
            $stmt = $this->db->prepare("
                UPDATE users
                SET is_active = CASE WHEN is_active = 1 THEN 0 ELSE 1 END
                WHERE id = ?
            ");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("خطأ في تغيير حالة المستخدم: " . $e->getMessage());
            return false;
        }
    }

    /**
     * التحقق من وجود اسم المستخدم
     */
    public function usernameExists($username, $excludeId = null) {
        try {
            $sql = "SELECT COUNT(*) FROM users WHERE username = ?";
            $params = [$username];

            if ($excludeId) {
                $sql .= " AND id != ?";
                $params[] = $excludeId;
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            error_log("خطأ في التحقق من اسم المستخدم: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على عدد المستخدمين
     */
    public function getUserCount() {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM users");
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("خطأ في عد المستخدمين: " . $e->getMessage());
            return 0;
        }
    }
}
?>
