<?php
/**
 * دوال مساعدة للتطبيق
 * Helper Functions
 */

/**
 * دالة رفع الملفات
 */
function uploadFile($file, $targetDir = 'achievements') {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > MAX_FILE_SIZE) {
        return false;
    }
    
    // التحقق من نوع الملف
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($fileExtension, ALLOWED_IMAGE_TYPES)) {
        return false;
    }
    
    // إنشاء اسم ملف فريد
    $fileName = uniqid() . '_' . time() . '.' . $fileExtension;
    $targetPath = UPLOAD_PATH . $targetDir . '/';
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!is_dir($targetPath)) {
        mkdir($targetPath, 0755, true);
    }
    
    $fullPath = $targetPath . $fileName;
    
    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $fullPath)) {
        return $targetDir . '/' . $fileName;
    }
    
    return false;
}

/**
 * دالة حذف الملف
 */
function deleteFile($filePath) {
    if ($filePath && file_exists(UPLOAD_PATH . $filePath)) {
        return unlink(UPLOAD_PATH . $filePath);
    }
    return false;
}

/**
 * دالة تنسيق التاريخ
 */
function formatDate($date, $format = 'Y-m-d') {
    if (!$date) return '';
    return date($format, strtotime($date));
}

/**
 * دالة تنسيق التاريخ للعرض بالعربية
 */
function formatDateArabic($date) {
    if (!$date) return '';
    
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);
    
    return "$day $month $year";
}

/**
 * دالة تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * دالة التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * دالة إنشاء رمز عشوائي
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * دالة تقليم النص
 */
function truncateText($text, $length = 100) {
    if (strlen($text) <= $length) {
        return $text;
    }
    return substr($text, 0, $length) . '...';
}

/**
 * دالة التحقق من صحة التاريخ
 */
function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * دالة الحصول على قائمة الجنسيات
 */
function getNationalities() {
    return [
        'سعودي', 'مصري', 'أردني', 'لبناني', 'سوري', 'عراقي', 'كويتي', 
        'إماراتي', 'قطري', 'بحريني', 'عماني', 'يمني', 'فلسطيني', 
        'مغربي', 'جزائري', 'تونسي', 'ليبي', 'سوداني', 'صومالي', 'أخرى'
    ];
}

/**
 * دالة الحصول على قائمة اللغات
 */
function getLanguages() {
    return [
        'العربية', 'الإنجليزية', 'الفرنسية', 'الألمانية', 'الإسبانية',
        'التركية', 'الفارسية', 'الأردية', 'الهندية', 'الماليزية',
        'الإندونيسية', 'الروسية', 'الصينية', 'اليابانية', 'أخرى'
    ];
}

/**
 * دالة الحصول على أنواع الأنشطة
 */
function getActivityTypes() {
    return ['محاضرة', 'دورة', 'توزيع مواد', 'استشارة', 'زيارة', 'أخرى'];
}

/**
 * دالة الحصول على حالات المعرقلات
 */
function getObstacleStatuses() {
    return ['مفتوح', 'قيد المعالجة', 'محلول', 'مؤجل'];
}
?>
