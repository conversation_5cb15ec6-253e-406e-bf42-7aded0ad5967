                </main>
            </div>
        </div>

        <!-- Footer -->
        <footer class="bg-light text-center py-3 mt-5">
            <div class="container">
                <p class="mb-0 text-muted">
                    &copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?> - الإصدار <?php echo APP_VERSION; ?>
                </p>
                <small class="text-muted">
                    تم التطوير بواسطة فريق التطوير - جمعية دعوة
                </small>
            </div>
        </footer>

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        
        <!-- Chart.js for reports -->
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        
        <!-- Custom JavaScript -->
        <script src="<?php echo APP_URL; ?>/public/js/script.js"></script>
        
        <script>
            // تأكيد الحذف
            function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
                return confirm(message);
            }
            
            // تأكيد تغيير الحالة
            function confirmToggle(message = 'هل أنت متأكد من تغيير الحالة؟') {
                return confirm(message);
            }
            
            // إخفاء الرسائل تلقائياً بعد 5 ثوان
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
            
            // تحسين تجربة المستخدم للنماذج
            document.addEventListener('DOMContentLoaded', function() {
                // إضافة تأثيرات للأزرار
                const buttons = document.querySelectorAll('.btn');
                buttons.forEach(function(button) {
                    button.addEventListener('click', function() {
                        this.style.transform = 'scale(0.95)';
                        setTimeout(() => {
                            this.style.transform = 'scale(1)';
                        }, 100);
                    });
                });
                
                // التحقق من صحة النماذج
                const forms = document.querySelectorAll('form');
                forms.forEach(function(form) {
                    form.addEventListener('submit', function(e) {
                        const requiredFields = form.querySelectorAll('[required]');
                        let isValid = true;
                        
                        requiredFields.forEach(function(field) {
                            if (!field.value.trim()) {
                                field.classList.add('is-invalid');
                                isValid = false;
                            } else {
                                field.classList.remove('is-invalid');
                            }
                        });
                        
                        if (!isValid) {
                            e.preventDefault();
                            alert('يرجى ملء جميع الحقول المطلوبة');
                        }
                    });
                });
                
                // إضافة تأثيرات للبطاقات
                const cards = document.querySelectorAll('.card');
                cards.forEach(function(card) {
                    card.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-5px)';
                    });
                    
                    card.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateY(0)';
                    });
                });
                
                // تحسين جداول البيانات
                const tables = document.querySelectorAll('.table');
                tables.forEach(function(table) {
                    // إضافة فئة للصفوف عند التمرير
                    const rows = table.querySelectorAll('tbody tr');
                    rows.forEach(function(row) {
                        row.addEventListener('mouseenter', function() {
                            this.style.backgroundColor = '#f8f9fa';
                        });
                        
                        row.addEventListener('mouseleave', function() {
                            this.style.backgroundColor = '';
                        });
                    });
                });
                
                // تحسين حقول البحث
                const searchInputs = document.querySelectorAll('input[type="search"]');
                searchInputs.forEach(function(input) {
                    let timeout;
                    input.addEventListener('input', function() {
                        clearTimeout(timeout);
                        timeout = setTimeout(() => {
                            // يمكن إضافة البحث المباشر هنا
                        }, 500);
                    });
                });
                
                // تحسين رفع الملفات
                const fileInputs = document.querySelectorAll('input[type="file"]');
                fileInputs.forEach(function(input) {
                    input.addEventListener('change', function() {
                        const file = this.files[0];
                        if (file) {
                            // التحقق من حجم الملف
                            if (file.size > 5 * 1024 * 1024) { // 5MB
                                alert('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
                                this.value = '';
                                return;
                            }
                            
                            // التحقق من نوع الملف
                            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                            if (!allowedTypes.includes(file.type)) {
                                alert('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)');
                                this.value = '';
                                return;
                            }
                            
                            // عرض معاينة الصورة
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                let preview = document.getElementById('image-preview');
                                if (!preview) {
                                    preview = document.createElement('img');
                                    preview.id = 'image-preview';
                                    preview.className = 'img-thumbnail mt-2';
                                    preview.style.maxWidth = '200px';
                                    input.parentNode.appendChild(preview);
                                }
                                preview.src = e.target.result;
                            };
                            reader.readAsDataURL(file);
                        }
                    });
                });
            });
            
            // دالة لتنسيق الأرقام
            function formatNumber(num) {
                return new Intl.NumberFormat('ar-SA').format(num);
            }
            
            // دالة لتنسيق التاريخ
            function formatDate(dateString) {
                const date = new Date(dateString);
                return date.toLocaleDateString('ar-SA', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            }
            
            // دالة للطباعة
            function printPage() {
                window.print();
            }
            
            // دالة لتصدير البيانات
            function exportData(format, url) {
                window.location.href = url + '&format=' + format;
            }
        </script>
    </body>
</html>
