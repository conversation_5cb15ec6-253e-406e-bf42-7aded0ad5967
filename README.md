# نظام إدارة شؤون الدعاة
## Da'wah Management System

نظام شامل لإدارة شؤون الدعاة في جمعية دعوة، مطور باستخدام PHP مع هيكل MVC وقاعدة بيانات MySQL.

---

## 🌟 الميزات الرئيسية

### 📋 إدارة معلومات الدعاة
- تسجيل البيانات الشخصية (الاسم، الجنسية، اللغة)
- معلومات التواصل (الجوال، البريد الإلكتروني)
- التخصص الدعوي والملاحظات

### 📅 إدارة الأنشطة الدعوية
- تسجيل الأنشطة مع التفاصيل الكاملة
- أنواع مختلفة من الأنشطة (محاضرة، دورة، توزيع مواد، إلخ)
- تتبع عدد الحضور والملاحظات

### 🏆 تسجيل الإنجازات
- توثيق الإنجازات مع الوصف والتاريخ
- تتبع عدد المستفيدين
- إمكانية رفع الصور المرفقة

### ⚠️ إدارة المعرقلات
- تسجيل المشاكل والمعرقلات
- تحديد الجهة المسؤولة عن الحل
- متابعة حالة المعرقل (مفتوح، قيد المعالجة، محلول، مؤجل)

### 👥 نظام المستخدمين والصلاحيات
- **المشرف**: صلاحيات كاملة لإدارة النظام
- **الداعية**: إدارة بياناته الشخصية فقط

### 📊 التقارير والإحصائيات
- تقارير شاملة حسب الفترة الزمنية
- إحصائيات مفصلة للأنشطة والإنجازات
- إمكانية تصدير التقارير بصيغة CSV

---

## 🛠️ المتطلبات التقنية

### متطلبات الخادم
- **PHP**: الإصدار 7.4 أو أحدث
- **MySQL**: الإصدار 5.7 أو أحدث
- **Apache/Nginx**: خادم ويب
- **PDO Extension**: لقاعدة البيانات

### المتطلبات الإضافية
- دعم UTF-8 للغة العربية
- إعدادات PHP:
  - `file_uploads = On`
  - `upload_max_filesize = 5M`
  - `post_max_size = 10M`

---

## 📦 التثبيت والإعداد

### 1. تحميل الملفات
```bash
# استنساخ المشروع أو تحميل الملفات
git clone [repository-url]
# أو تحميل الملفات إلى مجلد الخادم
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE dawah_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الجداول
mysql -u username -p dawah_management < sql/database.sql
```

### 3. تكوين الاتصال بقاعدة البيانات
قم بتعديل الملف `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'dawah_management');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 4. إعداد الصلاحيات
```bash
# إعطاء صلاحيات الكتابة لمجلد الرفع
chmod 755 public/uploads/
chown www-data:www-data public/uploads/
```

### 5. تكوين الخادم
#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]
```

#### Nginx
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}
```

---

## 🚀 بدء الاستخدام

### 1. الوصول للنظام
افتح المتصفح وانتقل إلى:
```
http://localhost/da
```

### 2. تسجيل الدخول
استخدم الحسابات التجريبية:

**حساب المشرف:**
- اسم المستخدم: `admin`
- كلمة المرور: `password`

**حساب الداعية:**
- اسم المستخدم: `preacher1`
- كلمة المرور: `password`

### 3. تغيير كلمات المرور
⚠️ **مهم**: قم بتغيير كلمات المرور الافتراضية فوراً بعد التثبيت.

---

## 📁 هيكل المشروع

```
da/
├── config/                 # ملفات الإعدادات
│   ├── database.php       # إعدادات قاعدة البيانات
│   └── config.php         # الإعدادات العامة
├── controllers/           # المتحكمات (Controllers)
│   ├── AuthController.php
│   ├── DashboardController.php
│   └── PreacherController.php
├── models/               # النماذج (Models)
│   ├── User.php
│   ├── Preacher.php
│   ├── Activity.php
│   ├── Achievement.php
│   └── Obstacle.php
├── views/                # العروض (Views)
│   ├── layouts/          # التخطيطات العامة
│   ├── auth/            # صفحات المصادقة
│   ├── dashboard/       # لوحة التحكم
│   └── preachers/       # إدارة الدعاة
├── public/              # الملفات العامة
│   ├── css/            # ملفات الأنماط
│   ├── js/             # ملفات JavaScript
│   ├── images/         # الصور
│   └── uploads/        # الملفات المرفوعة
├── includes/           # الملفات المساعدة
│   ├── functions.php   # الدوال المساعدة
│   └── session.php     # إدارة الجلسات
├── sql/               # ملفات قاعدة البيانات
│   └── database.sql   # هيكل قاعدة البيانات
├── index.php          # الملف الرئيسي
└── README.md          # هذا الملف
```

---

## 🔧 الإعدادات المتقدمة

### تخصيص الإعدادات
قم بتعديل الملف `config/config.php`:
```php
// رابط التطبيق
define('APP_URL', 'http://your-domain.com/da');

// مدة انتهاء الجلسة (بالثواني)
define('SESSION_TIMEOUT', 3600);

// الحد الأقصى لحجم الملف (بالبايت)
define('MAX_FILE_SIZE', 5 * 1024 * 1024);
```

### إعداد البريد الإلكتروني
لإضافة إشعارات البريد الإلكتروني، قم بتكوين SMTP في `config/config.php`.

---

## 🛡️ الأمان

### إرشادات الأمان
1. **تغيير كلمات المرور الافتراضية**
2. **استخدام HTTPS في الإنتاج**
3. **تحديث PHP وMySQL بانتظام**
4. **عمل نسخ احتياطية دورية**
5. **مراجعة سجلات الأخطاء**

### حماية الملفات
```apache
# في ملف .htaccess
<Files "config/*.php">
    Order allow,deny
    Deny from all
</Files>
```

---

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بقاعدة البيانات
```
خطأ في الاتصال بقاعدة البيانات
```
**الحل**: تحقق من إعدادات قاعدة البيانات في `config/database.php`

#### خطأ في رفع الملفات
```
فشل في رفع الملف
```
**الحل**: تحقق من صلاحيات مجلد `public/uploads/`

#### صفحة فارغة أو خطأ 500
**الحل**: فعّل عرض الأخطاء في PHP وتحقق من سجل الأخطاء

---

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX

### الإبلاغ عن الأخطاء
يرجى الإبلاغ عن أي أخطاء أو مشاكل مع تفاصيل كاملة عن:
- نوع المتصفح والإصدار
- رسالة الخطأ
- الخطوات المؤدية للخطأ

---

## 📄 الترخيص

هذا المشروع مطور خصيصاً لجمعية دعوة. جميع الحقوق محفوظة.

---

## 🙏 شكر وتقدير

نشكر جميع من ساهم في تطوير هذا النظام وجعله حقيقة لخدمة العمل الدعوي.

## 🧪 اختبار النظام

بعد التثبيت، يمكنك اختبار النظام عبر:
```
http://localhost/da/test.php
```

هذا الملف سيتحقق من:
- إعدادات PHP
- الاتصال بقاعدة البيانات
- وجود الملفات المطلوبة
- عمل الدوال المساعدة

**⚠️ احذف ملف test.php بعد التأكد من عمل النظام**

## 📊 البيانات التجريبية

النظام يأتي مع بيانات تجريبية تشمل:
- 4 دعاة مع بيانات كاملة
- 6 أنشطة دعوية متنوعة
- 5 إنجازات مختلفة
- 5 معرقلات بحالات مختلفة

للمزيد من التفاصيل، راجع ملف `INSTALL.md`

---

**🎉 مبروك! النظام جاهز للاستخدام**

**تم التطوير بواسطة فريق التطوير - جمعية دعوة**
**الإصدار**: 1.0.0
**تاريخ الإصدار**: 2024
