<?php
$pageTitle = 'تسجيل الدخول - ' . APP_NAME;
include ROOT_PATH . '/views/layouts/header.php';
?>

<div class="container">
    <div class="row justify-content-center align-items-center min-vh-100">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0">
                <div class="card-body p-5">
                    <!-- Logo and Title -->
                    <div class="text-center mb-4">
                        <div class="mb-3">
                            <i class="fas fa-mosque fa-3x text-success"></i>
                        </div>
                        <h3 class="card-title fw-bold text-success"><?php echo APP_NAME; ?></h3>
                        <p class="text-muted">تسجيل الدخول إلى النظام</p>
                    </div>

                    <!-- Login Form -->
                    <form method="POST" action="<?php echo APP_URL; ?>/index.php?page=login&action=login">
                        <input type="hidden" name="csrf_token" value="<?php echo getCSRFToken(); ?>">
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-2"></i>
                                اسم المستخدم
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg" 
                                   id="username" 
                                   name="username" 
                                   required 
                                   autofocus
                                   placeholder="أدخل اسم المستخدم">
                        </div>

                        <div class="mb-4">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>
                                كلمة المرور
                            </label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control form-control-lg" 
                                       id="password" 
                                       name="password" 
                                       required
                                       placeholder="أدخل كلمة المرور">
                                <button class="btn btn-outline-secondary" 
                                        type="button" 
                                        id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </div>
                    </form>

                    <!-- Demo Accounts Info -->
                    <div class="mt-4">
                        <hr>
                        <div class="text-center">
                            <small class="text-muted">
                                <strong>حسابات تجريبية:</strong><br>
                                <span class="badge bg-primary me-2">مشرف: admin / password</span>
                                <span class="badge bg-info">داعية: preacher1 / password</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Info -->
            <div class="text-center mt-4">
                <small class="text-muted">
                    <?php echo APP_NAME; ?> - الإصدار <?php echo APP_VERSION; ?><br>
                    جمعية دعوة &copy; <?php echo date('Y'); ?>
                </small>
            </div>
        </div>
    </div>
</div>

<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    
    .card {
        border-radius: 15px;
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }
    
    .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    
    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 10px;
        font-weight: 600;
        padding: 12px;
        transition: all 0.3s ease;
    }
    
    .btn-success:hover {
        background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    }
    
    .input-group .btn {
        border-color: #ced4da;
    }
    
    .badge {
        font-size: 0.7em;
    }
    
    .text-success {
        color: #28a745 !important;
    }
    
    .fa-mosque {
        color: #28a745;
        filter: drop-shadow(0 2px 4px rgba(40, 167, 69, 0.3));
    }
</style>

<script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        
        if (!username || !password) {
            e.preventDefault();
            alert('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }
        
        if (username.length < 3) {
            e.preventDefault();
            alert('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
            return;
        }
        
        if (password.length < 6) {
            e.preventDefault();
            alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return;
        }
    });
    
    // Auto-focus on username field
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('username').focus();
    });
</script>

<?php include ROOT_PATH . '/views/layouts/footer.php'; ?>
