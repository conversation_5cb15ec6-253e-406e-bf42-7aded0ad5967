<?php
/**
 * إعدادات التطبيق المتقدمة
 * Advanced Application Configuration
 */

// إعدادات البيئة
define('APP_ENV', 'development'); // development, production, testing
define('APP_DEBUG', true); // false في الإنتاج

// إعدادات اللغة والمنطقة الزمنية
define('APP_LOCALE', 'ar');
define('APP_TIMEZONE', 'Asia/Riyadh');
define('APP_CHARSET', 'UTF-8');

// إعدادات الأمان المتقدمة
define('CSRF_TOKEN_EXPIRE', 3600); // انتهاء صلاحية رمز CSRF بالثواني
define('PASSWORD_MIN_LENGTH', 6);
define('LOGIN_ATTEMPTS_LIMIT', 5); // عدد محاولات تسجيل الدخول المسموحة
define('LOGIN_LOCKOUT_TIME', 900); // مدة الحظر بالثواني (15 دقيقة)

// إعدادات الملفات المرفوعة
define('UPLOAD_ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']);
define('UPLOAD_MAX_FILES', 5); // عدد الملفات المسموح رفعها في المرة الواحدة
define('UPLOAD_QUALITY', 85); // جودة ضغط الصور (1-100)

// إعدادات قاعدة البيانات المتقدمة
define('DB_BACKUP_PATH', ROOT_PATH . '/backups/');
define('DB_BACKUP_RETENTION_DAYS', 30); // عدد أيام الاحتفاظ بالنسخ الاحتياطية

// إعدادات التسجيل والمراقبة
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_PATH', ROOT_PATH . '/logs/');
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB

// إعدادات الإشعارات
define('NOTIFICATION_EMAIL_ENABLED', false);
define('NOTIFICATION_SMS_ENABLED', false);

// إعدادات التقارير
define('REPORT_CACHE_ENABLED', true);
define('REPORT_CACHE_DURATION', 3600); // ساعة واحدة
define('REPORT_MAX_RECORDS', 10000); // الحد الأقصى للسجلات في التقرير

// إعدادات الأداء
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 1800); // 30 دقيقة
define('PAGINATION_LIMIT', 20); // عدد السجلات في الصفحة الواحدة

// إعدادات البحث
define('SEARCH_MIN_LENGTH', 2); // الحد الأدنى لطول كلمة البحث
define('SEARCH_MAX_RESULTS', 100); // الحد الأقصى لنتائج البحث

// إعدادات التصدير
define('EXPORT_FORMATS', ['csv', 'excel', 'pdf']);
define('EXPORT_MAX_RECORDS', 5000);

// إعدادات الواجهة
define('UI_THEME', 'default'); // default, dark, light
define('UI_LANGUAGE_SWITCHER', false);
define('UI_ANIMATIONS_ENABLED', true);

// إعدادات التطوير
if (APP_ENV === 'development') {
    // تفعيل عرض الأخطاء
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
    
    // إعدادات التطوير
    define('DEV_TOOLBAR_ENABLED', true);
    define('DEV_QUERY_LOG_ENABLED', true);
} else {
    // إعدادات الإنتاج
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
    error_reporting(0);
    
    define('DEV_TOOLBAR_ENABLED', false);
    define('DEV_QUERY_LOG_ENABLED', false);
}

// دوال مساعدة للإعدادات
function getConfig($key, $default = null) {
    return defined($key) ? constant($key) : $default;
}

function isProduction() {
    return APP_ENV === 'production';
}

function isDevelopment() {
    return APP_ENV === 'development';
}

function isTesting() {
    return APP_ENV === 'testing';
}

// تطبيق إعدادات المنطقة الزمنية
date_default_timezone_set(APP_TIMEZONE);

// تطبيق إعدادات الترميز
if (function_exists('mb_internal_encoding')) {
    mb_internal_encoding(APP_CHARSET);
}

// إنشاء المجلدات المطلوبة إذا لم تكن موجودة
$requiredDirs = [
    UPLOAD_PATH,
    UPLOAD_PATH . 'achievements/',
    UPLOAD_PATH . 'temp/',
    ROOT_PATH . '/logs/',
    ROOT_PATH . '/cache/',
    ROOT_PATH . '/backups/'
];

foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        
        // إنشاء ملف index.html لحماية المجلد
        $indexFile = $dir . 'index.html';
        if (!file_exists($indexFile)) {
            file_put_contents($indexFile, '<!-- Access Denied -->');
        }
    }
}

// تسجيل بداية تشغيل التطبيق
if (function_exists('error_log')) {
    error_log('[' . date('Y-m-d H:i:s') . '] Application started - Environment: ' . APP_ENV);
}
?>
