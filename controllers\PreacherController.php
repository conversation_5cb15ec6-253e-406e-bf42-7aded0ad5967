<?php
/**
 * متحكم الدعاة
 * Preacher Controller
 */

require_once ROOT_PATH . '/models/Preacher.php';
require_once ROOT_PATH . '/models/User.php';

class PreacherController {
    private $preacherModel;
    private $userModel;
    
    public function __construct() {
        $this->preacherModel = new Preacher();
        $this->userModel = new User();
    }
    
    /**
     * عرض قائمة الدعاة
     */
    public function index() {
        requireLogin();
        
        $search = $_GET['search'] ?? '';
        
        if (!empty($search)) {
            $preachers = $this->preacherModel->search($search);
        } else {
            $preachers = $this->preacherModel->getAll();
        }
        
        $data = [
            'preachers' => $preachers,
            'search' => $search
        ];
        
        include ROOT_PATH . '/views/preachers/index.php';
    }
    
    /**
     * عرض صفحة إضافة داعية جديد
     */
    public function create() {
        requireAdmin();
        
        $data = [
            'nationalities' => getNationalities(),
            'languages' => getLanguages()
        ];
        
        include ROOT_PATH . '/views/preachers/create.php';
    }
    
    /**
     * معالجة إضافة داعية جديد
     */
    public function store() {
        requireAdmin();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(APP_URL . '/index.php?page=preachers&action=create');
        }
        
        // التحقق من رمز CSRF
        if (!validateCSRF($_POST['csrf_token'] ?? '')) {
            showMessage('رمز الأمان غير صحيح', 'error');
            redirect(APP_URL . '/index.php?page=preachers&action=create');
        }
        
        $data = [
            'user_id' => !empty($_POST['user_id']) ? (int)$_POST['user_id'] : null,
            'full_name' => sanitize($_POST['full_name'] ?? ''),
            'nationality' => sanitize($_POST['nationality'] ?? ''),
            'language' => sanitize($_POST['language'] ?? ''),
            'mobile_phone' => sanitize($_POST['mobile_phone'] ?? ''),
            'email' => sanitize($_POST['email'] ?? ''),
            'specialization' => sanitize($_POST['specialization'] ?? ''),
            'notes' => sanitize($_POST['notes'] ?? '')
        ];
        
        // التحقق من صحة البيانات
        $errors = $this->validatePreacherData($data);
        
        if (!empty($errors)) {
            showMessage(implode('<br>', $errors), 'error');
            redirect(APP_URL . '/index.php?page=preachers&action=create');
        }
        
        // إنشاء الداعية
        if ($this->preacherModel->create($data)) {
            showMessage('تم إضافة الداعية بنجاح', 'success');
            redirect(APP_URL . '/index.php?page=preachers');
        } else {
            showMessage('حدث خطأ أثناء إضافة الداعية', 'error');
            redirect(APP_URL . '/index.php?page=preachers&action=create');
        }
    }
    
    /**
     * عرض تفاصيل الداعية
     */
    public function show($id) {
        requireLogin();
        
        $preacher = $this->preacherModel->getById($id);
        
        if (!$preacher) {
            showMessage('الداعية غير موجود', 'error');
            redirect(APP_URL . '/index.php?page=preachers');
        }
        
        // التحقق من الصلاحيات
        if (!isAdmin() && !checkOwnership($id)) {
            showMessage('ليس لديك صلاحية لعرض هذه البيانات', 'error');
            redirect(APP_URL . '/index.php?page=dashboard');
        }
        
        // الحصول على إحصائيات الداعية
        $stats = $this->preacherModel->getStats($id);
        
        $data = [
            'preacher' => $preacher,
            'stats' => $stats
        ];
        
        include ROOT_PATH . '/views/preachers/show.php';
    }
    
    /**
     * عرض صفحة تعديل الداعية
     */
    public function edit($id) {
        requireLogin();
        
        $preacher = $this->preacherModel->getById($id);
        
        if (!$preacher) {
            showMessage('الداعية غير موجود', 'error');
            redirect(APP_URL . '/index.php?page=preachers');
        }
        
        // التحقق من الصلاحيات
        if (!isAdmin() && !checkOwnership($id)) {
            showMessage('ليس لديك صلاحية لتعديل هذه البيانات', 'error');
            redirect(APP_URL . '/index.php?page=dashboard');
        }
        
        $data = [
            'preacher' => $preacher,
            'nationalities' => getNationalities(),
            'languages' => getLanguages()
        ];
        
        include ROOT_PATH . '/views/preachers/edit.php';
    }
    
    /**
     * معالجة تعديل الداعية
     */
    public function update($id) {
        requireLogin();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(APP_URL . '/index.php?page=preachers&action=edit&id=' . $id);
        }
        
        $preacher = $this->preacherModel->getById($id);
        
        if (!$preacher) {
            showMessage('الداعية غير موجود', 'error');
            redirect(APP_URL . '/index.php?page=preachers');
        }
        
        // التحقق من الصلاحيات
        if (!isAdmin() && !checkOwnership($id)) {
            showMessage('ليس لديك صلاحية لتعديل هذه البيانات', 'error');
            redirect(APP_URL . '/index.php?page=dashboard');
        }
        
        // التحقق من رمز CSRF
        if (!validateCSRF($_POST['csrf_token'] ?? '')) {
            showMessage('رمز الأمان غير صحيح', 'error');
            redirect(APP_URL . '/index.php?page=preachers&action=edit&id=' . $id);
        }
        
        $data = [
            'full_name' => sanitize($_POST['full_name'] ?? ''),
            'nationality' => sanitize($_POST['nationality'] ?? ''),
            'language' => sanitize($_POST['language'] ?? ''),
            'mobile_phone' => sanitize($_POST['mobile_phone'] ?? ''),
            'email' => sanitize($_POST['email'] ?? ''),
            'specialization' => sanitize($_POST['specialization'] ?? ''),
            'notes' => sanitize($_POST['notes'] ?? '')
        ];
        
        // التحقق من صحة البيانات
        $errors = $this->validatePreacherData($data, $id);
        
        if (!empty($errors)) {
            showMessage(implode('<br>', $errors), 'error');
            redirect(APP_URL . '/index.php?page=preachers&action=edit&id=' . $id);
        }
        
        // تحديث الداعية
        if ($this->preacherModel->update($id, $data)) {
            showMessage('تم تحديث بيانات الداعية بنجاح', 'success');
            redirect(APP_URL . '/index.php?page=preachers&action=show&id=' . $id);
        } else {
            showMessage('حدث خطأ أثناء تحديث بيانات الداعية', 'error');
            redirect(APP_URL . '/index.php?page=preachers&action=edit&id=' . $id);
        }
    }
    
    /**
     * حذف الداعية
     */
    public function delete($id) {
        requireAdmin();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(APP_URL . '/index.php?page=preachers');
        }
        
        // التحقق من رمز CSRF
        if (!validateCSRF($_POST['csrf_token'] ?? '')) {
            showMessage('رمز الأمان غير صحيح', 'error');
            redirect(APP_URL . '/index.php?page=preachers');
        }
        
        $preacher = $this->preacherModel->getById($id);
        
        if (!$preacher) {
            showMessage('الداعية غير موجود', 'error');
            redirect(APP_URL . '/index.php?page=preachers');
        }
        
        // حذف الداعية
        if ($this->preacherModel->delete($id)) {
            showMessage('تم حذف الداعية بنجاح', 'success');
        } else {
            showMessage('حدث خطأ أثناء حذف الداعية', 'error');
        }
        
        redirect(APP_URL . '/index.php?page=preachers');
    }
    
    /**
     * تفعيل/إلغاء تفعيل الداعية
     */
    public function toggleActive($id) {
        requireAdmin();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(APP_URL . '/index.php?page=preachers');
        }
        
        // التحقق من رمز CSRF
        if (!validateCSRF($_POST['csrf_token'] ?? '')) {
            showMessage('رمز الأمان غير صحيح', 'error');
            redirect(APP_URL . '/index.php?page=preachers');
        }
        
        if ($this->preacherModel->toggleActive($id)) {
            showMessage('تم تغيير حالة الداعية بنجاح', 'success');
        } else {
            showMessage('حدث خطأ أثناء تغيير حالة الداعية', 'error');
        }
        
        redirect(APP_URL . '/index.php?page=preachers');
    }
    
    /**
     * التحقق من صحة بيانات الداعية
     */
    private function validatePreacherData($data, $excludeId = null) {
        $errors = [];
        
        if (empty($data['full_name'])) {
            $errors[] = 'الاسم الكامل مطلوب';
        }
        
        if (empty($data['nationality'])) {
            $errors[] = 'الجنسية مطلوبة';
        }
        
        if (empty($data['language'])) {
            $errors[] = 'اللغة مطلوبة';
        }
        
        if (!empty($data['mobile_phone']) && !validatePhone($data['mobile_phone'])) {
            $errors[] = 'رقم الجوال غير صحيح';
        }
        
        if (!empty($data['email']) && !validateEmail($data['email'])) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        return $errors;
    }
}
?>
