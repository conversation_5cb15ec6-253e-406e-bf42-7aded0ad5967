<?php
/**
 * اختبار صفحة التقارير
 * Test Reports Page
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once 'config/config.php';

echo "<h1>🧪 اختبار صفحة التقارير</h1>";

try {
    // التحقق من تسجيل الدخول
    if (!isLoggedIn()) {
        echo "<p style='color: red;'>❌ يجب تسجيل الدخول أولاً</p>";
        echo "<p><a href='index.php?page=login'>تسجيل الدخول</a></p>";
        exit;
    }
    
    echo "<p style='color: green;'>✓ تم تسجيل الدخول بنجاح</p>";
    echo "<p>المستخدم: " . htmlspecialchars($_SESSION['user_full_name']) . "</p>";
    echo "<p>الدور: " . ($_SESSION['user_role'] === 'admin' ? 'مشرف' : 'داعية') . "</p>";
    
    // تضمين المتحكمات
    require_once ROOT_PATH . '/controllers/DashboardController.php';
    require_once ROOT_PATH . '/models/Preacher.php';
    
    echo "<h2>1. اختبار DashboardController:</h2>";
    $controller = new DashboardController();
    echo "<p style='color: green;'>✓ تم إنشاء DashboardController بنجاح</p>";
    
    echo "<h2>2. اختبار نموذج الدعاة:</h2>";
    $preacherModel = new Preacher();
    $preachers = $preacherModel->getAll();
    echo "<p style='color: blue;'>عدد الدعاة: " . count($preachers) . "</p>";
    
    if (!empty($preachers)) {
        echo "<p>أول داعية: " . htmlspecialchars($preachers[0]['full_name']) . "</p>";
    }
    
    echo "<h2>3. اختبار ملف التقارير:</h2>";
    $reportsFile = ROOT_PATH . '/views/dashboard/reports.php';
    if (file_exists($reportsFile)) {
        echo "<p style='color: green;'>✓ ملف التقارير موجود: $reportsFile</p>";
    } else {
        echo "<p style='color: red;'>❌ ملف التقارير غير موجود: $reportsFile</p>";
    }
    
    echo "<h2>4. اختبار ملف التصدير:</h2>";
    $exportFile = ROOT_PATH . '/export_data.php';
    if (file_exists($exportFile)) {
        echo "<p style='color: green;'>✓ ملف التصدير موجود: $exportFile</p>";
    } else {
        echo "<p style='color: red;'>❌ ملف التصدير غير موجود: $exportFile</p>";
    }
    
    echo "<h2>5. اختبار الدوال المساعدة:</h2>";
    
    // اختبار حساب متوسط الخبرة
    $totalExperience = 0;
    $count = 0;
    foreach ($preachers as $preacher) {
        if (isset($preacher['experience_years']) && $preacher['experience_years'] > 0) {
            $totalExperience += $preacher['experience_years'];
            $count++;
        }
    }
    $avgExperience = $count > 0 ? round($totalExperience / $count, 1) : 0;
    echo "<p style='color: blue;'>متوسط سنوات الخبرة: $avgExperience</p>";
    
    // اختبار بيانات التخصصات
    $specializations = [];
    foreach ($preachers as $preacher) {
        $spec = $preacher['specialization'] ?? 'غير محدد';
        if (empty($spec)) {
            $spec = 'غير محدد';
        }
        $specializations[$spec] = ($specializations[$spec] ?? 0) + 1;
    }
    
    echo "<p style='color: blue;'>التخصصات:</p>";
    echo "<ul>";
    foreach ($specializations as $spec => $count) {
        echo "<li>$spec: $count</li>";
    }
    echo "</ul>";
    
    echo "<h2>✅ جميع الاختبارات نجحت!</h2>";
    
    echo "<h2>🔗 الروابط:</h2>";
    echo "<ul>";
    echo "<li><a href='index.php?page=dashboard&action=reports'>صفحة التقارير</a></li>";
    echo "<li><a href='export_data.php?type=preachers'>تصدير بيانات الدعاة</a></li>";
    if (isAdmin()) {
        echo "<li><a href='export_data.php?type=users'>تصدير المستخدمين</a></li>";
    }
    echo "<li><a href='export_data.php?type=full'>تصدير تقرير شامل</a></li>";
    echo "<li><a href='index.php?page=dashboard'>لوحة التحكم</a></li>";
    echo "</ul>";
    
    // محاولة تشغيل دالة التقارير
    echo "<h2>6. اختبار تشغيل دالة التقارير:</h2>";
    
    try {
        // محاكاة استدعاء دالة التقارير
        $_GET['page'] = 'dashboard';
        $_GET['action'] = 'reports';
        
        ob_start();
        $controller->reports();
        $output = ob_get_clean();
        
        if (strlen($output) > 100) {
            echo "<p style='color: green;'>✓ دالة التقارير تعمل بنجاح (طول الإخراج: " . strlen($output) . " حرف)</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ دالة التقارير تعمل لكن الإخراج قصير</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في دالة التقارير: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
