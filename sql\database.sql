-- قاعدة بيانات تطبيق إدارة شؤون الدعاة
-- Da'wah Management System Database

CREATE DATABASE IF NOT EXISTS dawah_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE dawah_management;

-- جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    role ENUM('admin', 'preacher') DEFAULT 'preacher',
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الدعاة
CREATE TABLE preachers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    full_name VARCHAR(100) NOT NULL,
    nationality VARCHAR(50),
    language VARCHAR(50),
    mobile_phone VARCHAR(20),
    email VARCHAR(100),
    specialization TEXT,
    notes TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول الأنشطة الدعوية
CREATE TABLE activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    preacher_id INT NOT NULL,
    activity_name VARCHAR(200) NOT NULL,
    execution_date DATE NOT NULL,
    activity_type ENUM('محاضرة', 'دورة', 'توزيع مواد', 'استشارة', 'زيارة', 'أخرى') NOT NULL,
    attendees_count INT DEFAULT 0,
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (preacher_id) REFERENCES preachers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول الإنجازات
CREATE TABLE achievements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    preacher_id INT NOT NULL,
    achievement_description TEXT NOT NULL,
    achievement_date DATE NOT NULL,
    beneficiaries_count INT DEFAULT 0,
    image_path VARCHAR(255),
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (preacher_id) REFERENCES preachers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول المعرقلات
CREATE TABLE obstacles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    preacher_id INT NOT NULL,
    problem_description TEXT NOT NULL,
    problem_date DATE NOT NULL,
    responsible_entity VARCHAR(200),
    status ENUM('مفتوح', 'قيد المعالجة', 'محلول', 'مؤجل') DEFAULT 'مفتوح',
    solution_description TEXT,
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (preacher_id) REFERENCES preachers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- إدراج بيانات تجريبية
-- إدراج مستخدم مشرف افتراضي
INSERT INTO users (username, password, full_name, email, role) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', '<EMAIL>', 'admin');

-- إدراج مستخدم داعية تجريبي
INSERT INTO users (username, password, full_name, email, role) VALUES
('preacher1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد محمد', '<EMAIL>', 'preacher');

-- إدراج مستخدم داعية تجريبي آخر
INSERT INTO users (username, password, full_name, email, role) VALUES
('preacher2', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'محمد عبدالله', '<EMAIL>', 'preacher');

-- إدراج بيانات الدعاة التجريبية
INSERT INTO preachers (user_id, full_name, nationality, language, mobile_phone, email, specialization, notes) VALUES
(2, 'أحمد محمد علي', 'سعودي', 'العربية', '+966501234567', '<EMAIL>', 'الفقه والعقيدة', 'داعية متخصص في الفقه الإسلامي والعقيدة'),
(3, 'محمد عبدالله الأحمد', 'مصري', 'العربية', '+201012345678', '<EMAIL>', 'التفسير والحديث', 'متخصص في علوم القرآن والحديث النبوي'),
(NULL, 'عبدالرحمن خالد', 'أردني', 'العربية', '+962791234567', '<EMAIL>', 'الدعوة والإرشاد', 'خبرة 15 سنة في مجال الدعوة والإرشاد'),
(NULL, 'يوسف إبراهيم', 'لبناني', 'العربية', '+96171234567', '<EMAIL>', 'الأخلاق والسلوك', 'متخصص في الأخلاق الإسلامية والتربية');

-- إدراج أنشطة تجريبية
INSERT INTO activities (preacher_id, activity_name, execution_date, activity_type, attendees_count, notes, created_by) VALUES
(1, 'محاضرة عن أهمية الصلاة', '2024-01-15', 'محاضرة', 150, 'محاضرة في المسجد الكبير', 1),
(1, 'دورة في الفقه الإسلامي', '2024-01-20', 'دورة', 80, 'دورة مكثفة لمدة 3 أيام', 1),
(2, 'توزيع مصاحف على الجاليات', '2024-01-25', 'توزيع مواد', 200, 'توزيع في الأحياء الشعبية', 1),
(1, 'استشارة شرعية للشباب', '2024-02-01', 'استشارة', 25, 'جلسة أسئلة وأجوبة', 2),
(3, 'زيارة دعوية للمستشفى', '2024-02-05', 'زيارة', 50, 'زيارة المرضى وتقديم الدعم', 1),
(2, 'محاضرة عن السيرة النبوية', '2024-02-10', 'محاضرة', 120, 'في قاعة المؤتمرات', 3);

-- إدراج إنجازات تجريبية
INSERT INTO achievements (preacher_id, achievement_description, achievement_date, beneficiaries_count, notes, created_by) VALUES
(1, 'إسلام 5 أشخاص من الجالية الآسيوية', '2024-01-30', 5, 'بعد سلسلة من اللقاءات الدعوية', 1),
(2, 'تأسيس مركز تحفيظ القرآن في الحي', '2024-02-15', 100, 'يخدم أطفال الحي وشبابه', 1),
(1, 'إقامة معرض للكتاب الإسلامي', '2024-02-20', 300, 'استمر لمدة أسبوع وحقق إقبالاً كبيراً', 2),
(3, 'تدريب 20 داعية جديد', '2024-03-01', 20, 'دورة تدريبية في أساليب الدعوة', 1),
(2, 'إنتاج سلسلة فيديوهات دعوية', '2024-03-10', 1000, '10 حلقات عن الأخلاق الإسلامية', 3);

-- إدراج معرقلات تجريبية
INSERT INTO obstacles (preacher_id, problem_description, problem_date, responsible_entity, status, solution_description, notes, created_by) VALUES
(1, 'نقص في المواد الدعوية المطبوعة', '2024-01-10', 'إدارة المطبوعات', 'محلول', 'تم توفير 1000 نسخة من الكتيبات', 'تم حل المشكلة خلال أسبوع', 1),
(2, 'صعوبة في الحصول على تصريح للفعاليات', '2024-01-25', 'البلدية', 'قيد المعالجة', NULL, 'في انتظار الرد من الجهات المختصة', 1),
(1, 'عدم توفر قاعة مناسبة للمحاضرات', '2024-02-05', 'إدارة المساجد', 'مفتوح', NULL, 'نحتاج قاعة تتسع لـ 200 شخص', 2),
(3, 'نقص في المتطوعين للأنشطة', '2024-02-12', 'إدارة التطوع', 'قيد المعالجة', 'تم الإعلان عن حاجة للمتطوعين', 'نحتاج 10 متطوعين إضافيين', 1),
(2, 'مشكلة في نظام الصوت بالمسجد', '2024-02-18', 'إدارة الصيانة', 'محلول', 'تم إصلاح النظام وتحديثه', 'تم حل المشكلة بتكلفة 5000 ريال', 3);

-- ملاحظة: كلمة المرور الافتراضية لجميع المستخدمين هي: password
