# دليل التثبيت السريع
## نظام إدارة شؤون الدعاة

---

## 🚀 التثبيت السريع (5 دقائق)

### 1. متطلبات النظام
- **WAMP/XAMPP/LAMP** مثبت ويعمل
- **PHP 7.4+** مع امتداد PDO
- **MySQL 5.7+**
- **Apache** مع mod_rewrite مفعل

### 2. تحميل الملفات
```bash
# انسخ جميع الملفات إلى مجلد الخادم
# مثال: C:\wamp64\www\da\
```

### 3. إنشاء قاعدة البيانات
```sql
-- افتح phpMyAdmin أو MySQL Command Line
-- أنشئ قاعدة البيانات
CREATE DATABASE dawah_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استورد الجداول
-- في phpMyAdmin: استورد ملف sql/database.sql
-- أو استخدم الأمر:
mysql -u root -p dawah_management < sql/database.sql
```

### 4. تكوين الاتصال
عدّل الملف `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'dawah_management');
define('DB_USER', 'root');        // اسم المستخدم
define('DB_PASS', '');            // كلمة المرور (فارغة في WAMP)
```

### 5. تعديل رابط التطبيق
عدّل الملف `config/config.php`:
```php
define('APP_URL', 'http://localhost/da');  // غيّر حسب مسار مشروعك
```

### 6. إعداد الصلاحيات
```bash
# في Linux/Mac
chmod 755 public/uploads/
chmod 755 public/uploads/achievements/

# في Windows (WAMP)
# تأكد من أن مجلد uploads قابل للكتابة
```

---

## 🔑 تسجيل الدخول

### الحسابات الافتراضية:

**حساب المشرف:**
- الرابط: `http://localhost/da`
- اسم المستخدم: `admin`
- كلمة المرور: `password`

**حساب الداعية:**
- اسم المستخدم: `preacher1`
- كلمة المرور: `password`

---

## ⚠️ مهم جداً

### بعد التثبيت مباشرة:
1. **غيّر كلمات المرور الافتراضية**
2. **احذف أو عطّل عرض الأخطاء في الإنتاج**
3. **تأكد من عمل النسخ الاحتياطية**

### في حالة المشاكل:
1. **تحقق من إعدادات قاعدة البيانات**
2. **تأكد من تفعيل mod_rewrite في Apache**
3. **تحقق من صلاحيات مجلد uploads**

---

## 📞 الدعم السريع

### مشاكل شائعة:

**خطأ "قاعدة البيانات غير موجودة":**
```sql
-- تأكد من إنشاء قاعدة البيانات أولاً
CREATE DATABASE dawah_management;
USE dawah_management;
-- ثم استورد ملف database.sql
```

**خطأ "الصفحة غير موجودة":**
- تحقق من تفعيل mod_rewrite
- تأكد من وجود ملف .htaccess

**خطأ في رفع الملفات:**
```bash
# تأكد من صلاحيات المجلد
chmod 755 public/uploads/
```

---

## ✅ اختبار التثبيت

1. افتح `http://localhost/da`
2. سجل دخول بحساب admin
3. جرب إضافة داعية جديد
4. تحقق من عمل لوحة التحكم

---

**🎉 مبروك! النظام جاهز للاستخدام**

للمزيد من التفاصيل، راجع ملف `README.md`
