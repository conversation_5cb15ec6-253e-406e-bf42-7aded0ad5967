<?php
/**
 * تصدير البيانات
 * Data Export
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/index.php?page=login');
}

$type = $_GET['type'] ?? 'preachers';

try {
    $db = Database::getInstance()->getConnection();
    
    if ($type === 'preachers') {
        exportPreachers($db);
    } elseif ($type === 'users') {
        exportUsers($db);
    } elseif ($type === 'full') {
        exportFullReport($db);
    } else {
        showMessage('نوع التصدير غير صحيح', 'error');
        redirect(APP_URL . '/index.php?page=dashboard');
    }
    
} catch (Exception $e) {
    error_log("خطأ في التصدير: " . $e->getMessage());
    showMessage('حدث خطأ أثناء التصدير', 'error');
    redirect(APP_URL . '/index.php?page=dashboard');
}

/**
 * تصدير بيانات الدعاة
 */
function exportPreachers($db) {
    $filename = "preachers_" . date('Y-m-d') . ".csv";
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    // إضافة BOM للدعم العربي
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // كتابة العناوين
    fputcsv($output, [
        'المعرف',
        'الاسم الكامل',
        'البريد الإلكتروني',
        'رقم الهاتف',
        'العنوان',
        'التخصص',
        'المؤهل العلمي',
        'سنوات الخبرة',
        'اللغات',
        'نبذة شخصية',
        'الحالة',
        'تاريخ التسجيل'
    ]);
    
    // جلب البيانات
    $stmt = $db->prepare("
        SELECT p.*, u.username, u.is_active 
        FROM preachers p 
        LEFT JOIN users u ON p.user_id = u.id 
        ORDER BY p.created_at DESC
    ");
    $stmt->execute();
    $preachers = $stmt->fetchAll();
    
    // كتابة البيانات
    foreach ($preachers as $preacher) {
        fputcsv($output, [
            $preacher['id'],
            $preacher['full_name'],
            $preacher['email'],
            $preacher['phone'],
            $preacher['address'],
            $preacher['specialization'],
            $preacher['education'],
            $preacher['experience_years'],
            $preacher['languages'],
            $preacher['bio'],
            $preacher['is_active'] ? 'نشط' : 'معطل',
            $preacher['created_at']
        ]);
    }
    
    fclose($output);
    exit;
}

/**
 * تصدير بيانات المستخدمين
 */
function exportUsers($db) {
    // التحقق من صلاحيات المشرف
    if (!isAdmin()) {
        showMessage('ليس لديك صلاحية لتصدير بيانات المستخدمين', 'error');
        redirect(APP_URL . '/index.php?page=dashboard');
    }
    
    $filename = "users_" . date('Y-m-d') . ".csv";
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    // إضافة BOM للدعم العربي
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // كتابة العناوين
    fputcsv($output, [
        'المعرف',
        'اسم المستخدم',
        'الاسم الكامل',
        'البريد الإلكتروني',
        'الدور',
        'الحالة',
        'تاريخ التسجيل'
    ]);
    
    // جلب البيانات
    $stmt = $db->prepare("
        SELECT id, username, full_name, email, role, is_active, created_at 
        FROM users 
        ORDER BY created_at DESC
    ");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    // كتابة البيانات
    foreach ($users as $user) {
        fputcsv($output, [
            $user['id'],
            $user['username'],
            $user['full_name'],
            $user['email'],
            $user['role'] === 'admin' ? 'مشرف' : 'داعية',
            $user['is_active'] ? 'نشط' : 'معطل',
            $user['created_at']
        ]);
    }
    
    fclose($output);
    exit;
}

/**
 * تصدير تقرير شامل
 */
function exportFullReport($db) {
    $filename = "full_report_" . date('Y-m-d') . ".csv";
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    // إضافة BOM للدعم العربي
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // تقرير الدعاة
    fputcsv($output, ['تقرير شامل - ' . date('Y-m-d')]);
    fputcsv($output, []);
    fputcsv($output, ['بيانات الدعاة']);
    fputcsv($output, [
        'المعرف',
        'الاسم الكامل',
        'البريد الإلكتروني',
        'التخصص',
        'سنوات الخبرة',
        'الحالة'
    ]);
    
    // جلب بيانات الدعاة
    $stmt = $db->prepare("
        SELECT p.*, u.is_active 
        FROM preachers p 
        LEFT JOIN users u ON p.user_id = u.id 
        ORDER BY p.full_name
    ");
    $stmt->execute();
    $preachers = $stmt->fetchAll();
    
    foreach ($preachers as $preacher) {
        fputcsv($output, [
            $preacher['id'],
            $preacher['full_name'],
            $preacher['email'],
            $preacher['specialization'],
            $preacher['experience_years'],
            $preacher['is_active'] ? 'نشط' : 'معطل'
        ]);
    }
    
    // إحصائيات
    fputcsv($output, []);
    fputcsv($output, ['الإحصائيات']);
    fputcsv($output, ['إجمالي الدعاة', count($preachers)]);
    fputcsv($output, ['الدعاة النشطين', count(array_filter($preachers, function($p) { return $p['is_active']; }))]);
    
    // حساب متوسط سنوات الخبرة
    $totalExperience = 0;
    $experienceCount = 0;
    foreach ($preachers as $preacher) {
        if ($preacher['experience_years'] > 0) {
            $totalExperience += $preacher['experience_years'];
            $experienceCount++;
        }
    }
    $avgExperience = $experienceCount > 0 ? round($totalExperience / $experienceCount, 1) : 0;
    fputcsv($output, ['متوسط سنوات الخبرة', $avgExperience]);
    
    // التخصصات
    $specializations = [];
    foreach ($preachers as $preacher) {
        $spec = $preacher['specialization'] ?: 'غير محدد';
        $specializations[$spec] = ($specializations[$spec] ?? 0) + 1;
    }
    
    fputcsv($output, []);
    fputcsv($output, ['التخصصات']);
    foreach ($specializations as $spec => $count) {
        fputcsv($output, [$spec, $count]);
    }
    
    fclose($output);
    exit;
}
?>
