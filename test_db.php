<?php
/**
 * اختبار قاعدة البيانات
 * Database Test
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>اختبار قاعدة البيانات</h1>";

try {
    // تضمين ملف قاعدة البيانات
    require_once 'config/database.php';
    echo "<p style='color: green;'>✓ تم تضمين ملف قاعدة البيانات</p>";
    
    // الحصول على الاتصال
    $db = Database::getInstance()->getConnection();
    echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // اختبار الجداول
    echo "<h2>اختبار الجداول:</h2>";
    $tables = ['users', 'preachers', 'activities', 'achievements', 'obstacles'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch();
            echo "<p style='color: green;'>✓ جدول $table: {$result['count']} سجل</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ خطأ في جدول $table: " . $e->getMessage() . "</p>";
        }
    }
    
    // اختبار بيانات المستخدمين
    echo "<h2>اختبار بيانات المستخدمين:</h2>";
    $stmt = $db->query("SELECT id, username, full_name, role FROM users LIMIT 5");
    $users = $stmt->fetchAll();
    
    if ($users) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>الدور</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['full_name']}</td>";
            echo "<td>{$user['role']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>لا توجد بيانات مستخدمين</p>";
    }
    
    echo "<h2>✅ قاعدة البيانات تعمل بشكل صحيح!</h2>";
    echo "<p><a href='index.php'>العودة للصفحة الرئيسية</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ: " . $e->getMessage() . "</p>";
    
    echo "<h3>خطوات حل المشكلة:</h3>";
    echo "<ol>";
    echo "<li>تأكد من تشغيل MySQL في WAMP</li>";
    echo "<li>تأكد من إنشاء قاعدة البيانات 'dawah_management'</li>";
    echo "<li>استورد ملف sql/database.sql</li>";
    echo "<li>تحقق من إعدادات الاتصال في config/database.php</li>";
    echo "</ol>";
    
    echo "<p><a href='create_database.php'>إنشاء قاعدة البيانات تلقائياً</a></p>";
}
?>
