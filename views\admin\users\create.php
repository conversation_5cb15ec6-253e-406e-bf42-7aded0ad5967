<?php
/**
 * صفحة إضافة مستخدم جديد
 * Add New User Page
 */

requireAdmin();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مستخدم جديد - <?php echo APP_NAME; ?></title>
    <link href="<?php echo APP_URL; ?>/assets/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="<?php echo APP_URL; ?>/assets/css/style.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <?php include ROOT_PATH . '/views/layouts/navbar.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include ROOT_PATH . '/views/layouts/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إضافة مستخدم جديد</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="<?php echo APP_URL; ?>/index.php?page=users" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                        <?php 
                        echo $_SESSION['message'];
                        unset($_SESSION['message'], $_SESSION['message_type']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <form method="POST" action="<?php echo APP_URL; ?>/index.php?page=users&action=store">
                    <input type="hidden" name="csrf_token" value="<?php echo getCSRFToken(); ?>">
                    
                    <div class="row">
                        <!-- بيانات المستخدم الأساسية -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">بيانات المستخدم الأساسية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">اسم المستخدم *</label>
                                        <input type="text" class="form-control" id="username" name="username" required>
                                        <div class="form-text">يجب أن يكون فريداً ولا يحتوي على مسافات</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="full_name" class="form-label">الاسم الكامل *</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="email" name="email">
                                    </div>

                                    <div class="mb-3">
                                        <label for="role" class="form-label">نوع المستخدم *</label>
                                        <select class="form-select" id="role" name="role" required onchange="togglePreacherFields()">
                                            <option value="preacher">داعية</option>
                                            <option value="admin">مشرف</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="password" class="form-label">كلمة المرور *</label>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- بيانات الداعية (تظهر فقط إذا كان المستخدم داعية) -->
                        <div class="col-md-6" id="preacher-fields">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">بيانات الداعية</h5>
                                    <small class="text-muted">تظهر فقط للمستخدمين من نوع "داعية"</small>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone" name="phone">
                                    </div>

                                    <div class="mb-3">
                                        <label for="address" class="form-label">العنوان</label>
                                        <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label for="specialization" class="form-label">التخصص</label>
                                        <input type="text" class="form-control" id="specialization" name="specialization" 
                                               placeholder="مثال: الفقه، العقيدة، التفسير">
                                    </div>

                                    <div class="mb-3">
                                        <label for="education" class="form-label">المؤهل العلمي</label>
                                        <input type="text" class="form-control" id="education" name="education" 
                                               placeholder="مثال: بكالوريوس شريعة">
                                    </div>

                                    <div class="mb-3">
                                        <label for="experience_years" class="form-label">سنوات الخبرة</label>
                                        <input type="number" class="form-control" id="experience_years" name="experience_years" 
                                               min="0" max="50" value="0">
                                    </div>

                                    <div class="mb-3">
                                        <label for="languages" class="form-label">اللغات</label>
                                        <input type="text" class="form-control" id="languages" name="languages" 
                                               placeholder="مثال: العربية، الإنجليزية">
                                    </div>

                                    <div class="mb-3">
                                        <label for="bio" class="form-label">نبذة شخصية</label>
                                        <textarea class="form-control" id="bio" name="bio" rows="3" 
                                                  placeholder="نبذة مختصرة عن الداعية وخبراته"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="<?php echo APP_URL; ?>/index.php?page=users" class="btn btn-secondary me-md-2">
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> إنشاء المستخدم
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="<?php echo APP_URL; ?>/assets/js/bootstrap.bundle.min.js"></script>
    <script>
        function togglePreacherFields() {
            const role = document.getElementById('role').value;
            const preacherFields = document.getElementById('preacher-fields');
            
            if (role === 'preacher') {
                preacherFields.style.display = 'block';
            } else {
                preacherFields.style.display = 'none';
            }
        }

        // تشغيل الدالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            togglePreacherFields();
        });

        // التحقق من تطابق كلمات المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
