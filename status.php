<?php
/**
 * صفحة حالة النظام
 * System Status Page
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة النظام - نظام إدارة شؤون الدعاة</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 10px; text-align: right; border: 1px solid #ddd; }
        th { background: #f8f9fa; }
        .status-ok { background: #d4edda; }
        .status-error { background: #f8d7da; }
        .status-warning { background: #fff3cd; }
        h1, h2 { color: #333; }
        .btn { 
            display: inline-block; 
            padding: 10px 20px; 
            margin: 5px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
        }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 حالة النظام</h1>
        <p><strong>التاريخ والوقت:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <h2>📊 معلومات الخادم</h2>
        <table>
            <tr>
                <th>العنصر</th>
                <th>القيمة</th>
                <th>الحالة</th>
            </tr>
            <tr class="status-ok">
                <td>إصدار PHP</td>
                <td><?php echo PHP_VERSION; ?></td>
                <td><span class="success">✓ يعمل</span></td>
            </tr>
            <tr class="<?php echo extension_loaded('pdo') ? 'status-ok' : 'status-error'; ?>">
                <td>امتداد PDO</td>
                <td><?php echo extension_loaded('pdo') ? 'مثبت' : 'غير مثبت'; ?></td>
                <td><span class="<?php echo extension_loaded('pdo') ? 'success' : 'error'; ?>">
                    <?php echo extension_loaded('pdo') ? '✓ يعمل' : '✗ خطأ'; ?>
                </span></td>
            </tr>
            <tr class="<?php echo extension_loaded('pdo_mysql') ? 'status-ok' : 'status-error'; ?>">
                <td>امتداد PDO MySQL</td>
                <td><?php echo extension_loaded('pdo_mysql') ? 'مثبت' : 'غير مثبت'; ?></td>
                <td><span class="<?php echo extension_loaded('pdo_mysql') ? 'success' : 'error'; ?>">
                    <?php echo extension_loaded('pdo_mysql') ? '✓ يعمل' : '✗ خطأ'; ?>
                </span></td>
            </tr>
            <tr class="status-ok">
                <td>نظام التشغيل</td>
                <td><?php echo PHP_OS; ?></td>
                <td><span class="info">ℹ معلومات</span></td>
            </tr>
        </table>

        <h2>📁 حالة الملفات</h2>
        <table>
            <tr>
                <th>الملف</th>
                <th>الحالة</th>
                <th>الحجم</th>
            </tr>
            <?php
            $files = [
                'config/config.php' => 'ملف الإعدادات',
                'config/database.php' => 'ملف قاعدة البيانات',
                'includes/functions.php' => 'ملف الدوال',
                'includes/session.php' => 'ملف الجلسات',
                '.htaccess' => 'ملف Apache',
                'sql/database.sql' => 'ملف قاعدة البيانات'
            ];
            
            foreach ($files as $file => $description) {
                $exists = file_exists($file);
                $size = $exists ? filesize($file) : 0;
                $class = $exists ? 'status-ok' : 'status-error';
                echo "<tr class='$class'>";
                echo "<td>$description ($file)</td>";
                echo "<td><span class='" . ($exists ? 'success' : 'error') . "'>";
                echo $exists ? '✓ موجود' : '✗ غير موجود';
                echo "</span></td>";
                echo "<td>" . ($exists ? number_format($size) . ' بايت' : '-') . "</td>";
                echo "</tr>";
            }
            ?>
        </table>

        <h2>🗄️ حالة قاعدة البيانات</h2>
        <?php
        try {
            require_once 'config/database.php';
            $db = Database::getInstance()->getConnection();
            echo "<p class='success'>✓ الاتصال بقاعدة البيانات ناجح</p>";
            
            echo "<table>";
            echo "<tr><th>الجدول</th><th>عدد السجلات</th><th>الحالة</th></tr>";
            
            $tables = ['users', 'preachers', 'activities', 'achievements', 'obstacles'];
            foreach ($tables as $table) {
                try {
                    $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
                    $result = $stmt->fetch();
                    $count = $result['count'];
                    echo "<tr class='status-ok'>";
                    echo "<td>$table</td>";
                    echo "<td>$count</td>";
                    echo "<td><span class='success'>✓ يعمل</span></td>";
                    echo "</tr>";
                } catch (Exception $e) {
                    echo "<tr class='status-error'>";
                    echo "<td>$table</td>";
                    echo "<td>-</td>";
                    echo "<td><span class='error'>✗ خطأ</span></td>";
                    echo "</tr>";
                }
            }
            echo "</table>";
            
        } catch (Exception $e) {
            echo "<p class='error'>✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
        }
        ?>

        <h2>🔗 روابط مفيدة</h2>
        <div>
            <a href="index.php" class="btn">الصفحة الرئيسية</a>
            <a href="test_db.php" class="btn">اختبار قاعدة البيانات</a>
            <a href="hello.php" class="btn">اختبار PHP</a>
            <a href="info.php" class="btn">معلومات PHP</a>
            <?php if (file_exists('create_database.php')): ?>
            <a href="create_database.php" class="btn">إنشاء قاعدة البيانات</a>
            <?php endif; ?>
        </div>

        <h2>📋 ملاحظات</h2>
        <ul>
            <li>إذا كانت جميع العناصر تعمل بشكل صحيح، يمكنك البدء في استخدام النظام</li>
            <li>إذا كان هناك أخطاء في قاعدة البيانات، استخدم رابط "إنشاء قاعدة البيانات"</li>
            <li>تأكد من تشغيل WAMP Server بشكل صحيح</li>
            <li>في حالة وجود مشاكل، راجع ملف WAMP_SETUP.md</li>
        </ul>
    </div>
</body>
</html>
