<?php
/**
 * الشريط الجانبي
 * Sidebar Navigation
 */

$currentPage = $_GET['page'] ?? 'dashboard';
?>

<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <!-- لوحة التحكم -->
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'dashboard' ? 'active' : ''; ?>" 
                   href="<?php echo APP_URL; ?>/index.php?page=dashboard">
                    <i class="bi bi-house-door"></i>
                    لوحة التحكم
                </a>
            </li>

            <!-- إدارة الدعاة -->
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'preachers' ? 'active' : ''; ?>" 
                   href="<?php echo APP_URL; ?>/index.php?page=preachers">
                    <i class="bi bi-people"></i>
                    إدارة الدعاة
                </a>
            </li>

            <!-- إدارة المستخدمين (للمشرفين فقط) -->
            <?php if (isAdmin()): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentPage === 'users' ? 'active' : ''; ?>" 
                       href="<?php echo APP_URL; ?>/index.php?page=users">
                        <i class="bi bi-person-gear"></i>
                        إدارة المستخدمين
                    </a>
                </li>
            <?php endif; ?>

            <!-- الأنشطة -->
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'activities' ? 'active' : ''; ?>" 
                   href="<?php echo APP_URL; ?>/index.php?page=activities">
                    <i class="bi bi-calendar-event"></i>
                    الأنشطة
                </a>
            </li>

            <!-- الإنجازات -->
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'achievements' ? 'active' : ''; ?>" 
                   href="<?php echo APP_URL; ?>/index.php?page=achievements">
                    <i class="bi bi-trophy"></i>
                    الإنجازات
                </a>
            </li>

            <!-- المعرقلات -->
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'obstacles' ? 'active' : ''; ?>" 
                   href="<?php echo APP_URL; ?>/index.php?page=obstacles">
                    <i class="bi bi-exclamation-triangle"></i>
                    المعرقلات
                </a>
            </li>
        </ul>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>التقارير</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link" href="<?php echo APP_URL; ?>/index.php?page=dashboard&action=reports">
                    <i class="bi bi-graph-up"></i>
                    التقارير الإحصائية
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="<?php echo APP_URL; ?>/index.php?page=dashboard&action=export">
                    <i class="bi bi-download"></i>
                    تصدير البيانات
                </a>
            </li>
        </ul>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>الحساب</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link" href="<?php echo APP_URL; ?>/index.php?page=change_password">
                    <i class="bi bi-key"></i>
                    تغيير كلمة المرور
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="<?php echo APP_URL; ?>/index.php?page=logout" 
                   onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                    <i class="bi bi-box-arrow-right"></i>
                    تسجيل الخروج
                </a>
            </li>
        </ul>
    </div>
</nav>

<style>
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    font-weight: 600;
}

.sidebar .nav-link i {
    margin-left: 0.5rem;
    width: 16px;
    text-align: center;
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
}
</style>
