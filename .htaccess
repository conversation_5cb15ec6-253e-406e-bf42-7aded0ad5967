# تطبيق إدارة شؤون الدعاة - إعدادات Apache
# Da'wah Management System - Apache Configuration

# تفعيل عرض الأخطاء (للتطوير فقط)
php_flag display_errors On
php_flag display_startup_errors On
php_value error_reporting "E_ALL"

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# إعادة توجيه جميع الطلبات إلى index.php (ما عدا الملفات الموجودة)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/(debug\.php|test\.php)$
RewriteRule ^(.*)$ index.php [QSA,L]

# حماية ملفات الإعدادات
<FilesMatch "\.(php|inc)$">
    <IfModule mod_authz_core.c>
        Require all denied
    </IfModule>
    <IfModule !mod_authz_core.c>
        Order allow,deny
        Deny from all
    </IfModule>
</FilesMatch>

# السماح بالوصول للملف الرئيسي فقط
<Files "index.php">
    <IfModule mod_authz_core.c>
        Require all granted
    </IfModule>
    <IfModule !mod_authz_core.c>
        Order allow,deny
        Allow from all
    </IfModule>
</Files>

# حماية مجلدات النظام
<IfModule mod_alias.c>
    RedirectMatch 403 ^/da/(config|includes|models|controllers|views|sql)/.*$
</IfModule>

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع عرض معلومات الخادم
    Header always unset Server
    Header always unset X-Powered-By

    # حماية من XSS
    Header always set X-XSS-Protection "1; mode=block"

    # منع MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"

    # حماية من Clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"

    # إعدادات HTTPS (فعّل عند استخدام SSL)
    # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-icon "access plus 1 month"
    ExpiresByType application/x-icon "access plus 1 month"
</IfModule>

# إعدادات PHP
<IfModule mod_php7.c>
    # تفعيل عرض الأخطاء في بيئة التطوير (أزل في الإنتاج)
    php_flag display_errors On
    php_flag display_startup_errors On
    php_value error_reporting "E_ALL"

    # إعدادات الجلسة
    php_value session.cookie_httponly 1
    php_value session.use_only_cookies 1
    php_value session.cookie_secure 0

    # إعدادات رفع الملفات
    php_value upload_max_filesize "5M"
    php_value post_max_size "10M"
    php_value max_execution_time 300
    php_value memory_limit "128M"

    # إعدادات الترميز
    php_value default_charset "UTF-8"
    php_value mbstring.internal_encoding "UTF-8"
</IfModule>

# منع الوصول للملفات الحساسة
<Files ".htaccess">
    <IfModule mod_authz_core.c>
        Require all denied
    </IfModule>
    <IfModule !mod_authz_core.c>
        Order allow,deny
        Deny from all
    </IfModule>
</Files>

<Files "README.md">
    <IfModule mod_authz_core.c>
        Require all denied
    </IfModule>
    <IfModule !mod_authz_core.c>
        Order allow,deny
        Deny from all
    </IfModule>
</Files>

# السماح بالوصول للملفات العامة
<IfModule mod_alias.c>
    # السماح بالوصول لملفات CSS و JS والصور
    <LocationMatch "^/da/public/(css|js|images|uploads)/">
        <IfModule mod_authz_core.c>
            Require all granted
        </IfModule>
        <IfModule !mod_authz_core.c>
            Order allow,deny
            Allow from all
        </IfModule>
    </LocationMatch>
</IfModule>
