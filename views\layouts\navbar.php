<?php
/**
 * شريط التنقل العلوي
 * Top Navigation Bar
 */
?>

<header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
    <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="<?php echo APP_URL; ?>/index.php">
        <?php echo APP_NAME; ?>
    </a>
    
    <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" 
            data-bs-toggle="collapse" data-bs-target="#sidebarMenu" 
            aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    
    <div class="navbar-nav">
        <div class="nav-item text-nowrap">
            <div class="dropdown">
                <a class="nav-link px-3 dropdown-toggle" href="#" role="button" 
                   data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-person-circle"></i>
                    <?php echo htmlspecialchars($_SESSION['user_full_name'] ?? 'المستخدم'); ?>
                    <?php if (isAdmin()): ?>
                        <span class="badge bg-danger ms-1">مشرف</span>
                    <?php endif; ?>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <h6 class="dropdown-header">
                            <?php echo htmlspecialchars($_SESSION['user_full_name'] ?? 'المستخدم'); ?>
                        </h6>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="<?php echo APP_URL; ?>/index.php?page=dashboard">
                            <i class="bi bi-house-door"></i> لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="<?php echo APP_URL; ?>/index.php?page=change_password">
                            <i class="bi bi-key"></i> تغيير كلمة المرور
                        </a>
                    </li>
                    <?php if (isAdmin()): ?>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>/index.php?page=users">
                                <i class="bi bi-person-gear"></i> إدارة المستخدمين
                            </a>
                        </li>
                    <?php endif; ?>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item text-danger" href="<?php echo APP_URL; ?>/index.php?page=logout"
                           onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                            <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</header>

<style>
.navbar-brand {
    font-weight: bold;
    font-size: 1.25rem;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-item {
    padding: 0.5rem 1rem;
}

.dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.dropdown-item i {
    margin-left: 0.5rem;
    width: 16px;
    text-align: center;
}

.navbar-toggler {
    right: 1rem;
    top: 0.5rem;
}

@media (max-width: 767.98px) {
    .navbar-brand {
        font-size: 1rem;
    }
}
</style>
