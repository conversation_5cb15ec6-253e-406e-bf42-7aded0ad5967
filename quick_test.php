<?php
/**
 * اختبار سريع للنظام
 * Quick System Test
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>🚀 اختبار سريع للنظام</h1>";

try {
    // تضمين الملفات الأساسية
    echo "<h2>1. تضمين الملفات:</h2>";
    
    require_once 'config/database.php';
    echo "<p style='color: green;'>✓ تم تضمين config/database.php</p>";
    
    require_once 'config/config.php';
    echo "<p style='color: green;'>✓ تم تضمين config/config.php</p>";
    
    require_once 'includes/functions.php';
    echo "<p style='color: green;'>✓ تم تضمين includes/functions.php</p>";
    
    require_once 'models/User.php';
    echo "<p style='color: green;'>✓ تم تضمين models/User.php</p>";
    
    require_once 'controllers/AuthController.php';
    echo "<p style='color: green;'>✓ تم تضمين controllers/AuthController.php</p>";
    
    // اختبار قاعدة البيانات
    echo "<h2>2. اختبار قاعدة البيانات:</h2>";
    $db = Database::getInstance()->getConnection();
    echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات ناجح</p>";
    
    // اختبار نموذج المستخدم
    echo "<h2>3. اختبار نموذج المستخدم:</h2>";
    $userModel = new User();
    $userCount = $userModel->getUserCount();
    echo "<p style='color: blue;'>عدد المستخدمين: $userCount</p>";
    
    // اختبار AuthController
    echo "<h2>4. اختبار AuthController:</h2>";
    $authController = new AuthController();
    echo "<p style='color: green;'>✓ تم إنشاء AuthController بنجاح</p>";
    
    // اختبار الجلسة
    echo "<h2>5. اختبار الجلسة:</h2>";
    echo "<p style='color: blue;'>حالة الجلسة: " . session_status() . "</p>";
    echo "<p style='color: blue;'>معرف الجلسة: " . session_id() . "</p>";
    
    echo "<h2>✅ جميع الاختبارات نجحت!</h2>";
    
    echo "<h2>🔗 الروابط:</h2>";
    echo "<ul>";
    echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
    echo "<li><a href='index.php?page=register'>التسجيل</a></li>";
    echo "<li><a href='index.php?page=login'>تسجيل الدخول</a></li>";
    echo "<li><a href='status.php'>حالة النظام</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
