<?php
/**
 * نموذج الإنجاز
 * Achievement Model
 */

class Achievement {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * إنشاء إنجاز جديد
     */
    public function create($data) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO achievements (preacher_id, achievement_description, achievement_date, 
                                        beneficiaries_count, image_path, notes, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            return $stmt->execute([
                $data['preacher_id'],
                $data['achievement_description'],
                $data['achievement_date'],
                $data['beneficiaries_count'] ?? 0,
                $data['image_path'] ?? null,
                $data['notes'] ?? null,
                $_SESSION['user_id']
            ]);
        } catch (PDOException $e) {
            error_log("خطأ في إنشاء الإنجاز: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على إنجاز بواسطة المعرف
     */
    public function getById($id) {
        try {
            $stmt = $this->db->prepare("
                SELECT a.*, p.full_name as preacher_name, u.full_name as created_by_name
                FROM achievements a 
                LEFT JOIN preachers p ON a.preacher_id = p.id 
                LEFT JOIN users u ON a.created_by = u.id 
                WHERE a.id = ?
            ");
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("خطأ في جلب الإنجاز: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على جميع الإنجازات
     */
    public function getAll($preacherId = null, $limit = null) {
        try {
            $sql = "
                SELECT a.*, p.full_name as preacher_name, u.full_name as created_by_name
                FROM achievements a 
                LEFT JOIN preachers p ON a.preacher_id = p.id 
                LEFT JOIN users u ON a.created_by = u.id
            ";
            
            $params = [];
            
            if ($preacherId) {
                $sql .= " WHERE a.preacher_id = ?";
                $params[] = $preacherId;
            }
            
            $sql .= " ORDER BY a.achievement_date DESC";
            
            if ($limit) {
                $sql .= " LIMIT ?";
                $params[] = $limit;
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في جلب الإنجازات: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث الإنجاز
     */
    public function update($id, $data) {
        try {
            $sql = "
                UPDATE achievements 
                SET achievement_description = ?, achievement_date = ?, 
                    beneficiaries_count = ?, notes = ?
            ";
            
            $params = [
                $data['achievement_description'],
                $data['achievement_date'],
                $data['beneficiaries_count'] ?? 0,
                $data['notes'] ?? null
            ];
            
            // تحديث الصورة إذا تم توفيرها
            if (isset($data['image_path'])) {
                $sql .= ", image_path = ?";
                $params[] = $data['image_path'];
            }
            
            $sql .= " WHERE id = ?";
            $params[] = $id;
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("خطأ في تحديث الإنجاز: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * حذف الإنجاز
     */
    public function delete($id) {
        try {
            // الحصول على مسار الصورة لحذفها
            $achievement = $this->getById($id);
            
            $stmt = $this->db->prepare("DELETE FROM achievements WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            // حذف الصورة إذا كانت موجودة
            if ($result && $achievement && $achievement['image_path']) {
                deleteFile($achievement['image_path']);
            }
            
            return $result;
        } catch (PDOException $e) {
            error_log("خطأ في حذف الإنجاز: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * البحث في الإنجازات
     */
    public function search($keyword, $preacherId = null) {
        try {
            $sql = "
                SELECT a.*, p.full_name as preacher_name, u.full_name as created_by_name
                FROM achievements a 
                LEFT JOIN preachers p ON a.preacher_id = p.id 
                LEFT JOIN users u ON a.created_by = u.id 
                WHERE (a.achievement_description LIKE ? OR a.notes LIKE ?)
            ";
            
            $params = ["%$keyword%", "%$keyword%"];
            
            if ($preacherId) {
                $sql .= " AND a.preacher_id = ?";
                $params[] = $preacherId;
            }
            
            $sql .= " ORDER BY a.achievement_date DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في البحث في الإنجازات: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على الإنجازات حسب التاريخ
     */
    public function getByDateRange($startDate, $endDate, $preacherId = null) {
        try {
            $sql = "
                SELECT a.*, p.full_name as preacher_name, u.full_name as created_by_name
                FROM achievements a 
                LEFT JOIN preachers p ON a.preacher_id = p.id 
                LEFT JOIN users u ON a.created_by = u.id 
                WHERE a.achievement_date BETWEEN ? AND ?
            ";
            
            $params = [$startDate, $endDate];
            
            if ($preacherId) {
                $sql .= " AND a.preacher_id = ?";
                $params[] = $preacherId;
            }
            
            $sql .= " ORDER BY a.achievement_date DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في جلب الإنجازات حسب التاريخ: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات الإنجازات
     */
    public function getStats($preacherId = null) {
        try {
            $stats = [];
            
            $whereClause = $preacherId ? "WHERE preacher_id = ?" : "";
            $params = $preacherId ? [$preacherId] : [];
            
            // إجمالي الإنجازات
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM achievements $whereClause");
            $stmt->execute($params);
            $stats['total_achievements'] = $stmt->fetchColumn();
            
            // إجمالي المستفيدين
            $stmt = $this->db->prepare("SELECT SUM(beneficiaries_count) FROM achievements $whereClause");
            $stmt->execute($params);
            $stats['total_beneficiaries'] = $stmt->fetchColumn() ?? 0;
            
            // الإنجازات الشهرية
            $stmt = $this->db->prepare("
                SELECT DATE_FORMAT(achievement_date, '%Y-%m') as month, COUNT(*) as count 
                FROM achievements $whereClause 
                GROUP BY month 
                ORDER BY month DESC 
                LIMIT 12
            ");
            $stmt->execute($params);
            $stats['monthly'] = $stmt->fetchAll();
            
            return $stats;
        } catch (PDOException $e) {
            error_log("خطأ في جلب إحصائيات الإنجازات: " . $e->getMessage());
            return [];
        }
    }
}
?>
