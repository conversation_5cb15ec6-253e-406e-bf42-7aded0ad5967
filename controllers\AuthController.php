<?php
/**
 * متحكم المصادقة
 * Authentication Controller
 */

require_once ROOT_PATH . '/models/User.php';
require_once ROOT_PATH . '/models/Preacher.php';

class AuthController {
    private $userModel;
    private $preacherModel;
    
    public function __construct() {
        $this->userModel = new User();
        $this->preacherModel = new Preacher();
    }
    
    /**
     * عرض صفحة تسجيل الدخول
     */
    public function showLogin() {
        // إعادة توجيه المستخدم المسجل دخوله إلى لوحة التحكم
        if (isLoggedIn()) {
            redirect(APP_URL . '/index.php?page=dashboard');
        }
        
        include ROOT_PATH . '/views/auth/login.php';
    }
    
    /**
     * معالجة تسجيل الدخول
     */
    public function login() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(APP_URL . '/index.php?page=login');
        }
        
        // التحقق من رمز CSRF
        if (!validateCSRF($_POST['csrf_token'] ?? '')) {
            showMessage('رمز الأمان غير صحيح', 'error');
            redirect(APP_URL . '/index.php?page=login');
        }
        
        $username = sanitize($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        // التحقق من صحة البيانات
        if (empty($username) || empty($password)) {
            showMessage('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
            redirect(APP_URL . '/index.php?page=login');
        }
        
        // محاولة تسجيل الدخول
        $user = $this->userModel->login($username, $password);
        
        if ($user) {
            // تسجيل دخول المستخدم
            loginUser($user);
            
            showMessage('تم تسجيل الدخول بنجاح', 'success');
            redirect(APP_URL . '/index.php?page=dashboard');
        } else {
            showMessage('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            redirect(APP_URL . '/index.php?page=login');
        }
    }
    
    /**
     * تسجيل الخروج
     */
    public function logout() {
        logoutUser();
        showMessage('تم تسجيل الخروج بنجاح', 'success');
        redirect(APP_URL . '/index.php?page=login');
    }
    
    /**
     * عرض صفحة التسجيل (للمشرفين فقط)
     */
    public function showRegister() {
        requireAdmin();
        
        include ROOT_PATH . '/views/auth/register.php';
    }
    
    /**
     * معالجة التسجيل
     */
    public function register() {
        requireAdmin();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(APP_URL . '/index.php?page=register');
        }
        
        // التحقق من رمز CSRF
        if (!validateCSRF($_POST['csrf_token'] ?? '')) {
            showMessage('رمز الأمان غير صحيح', 'error');
            redirect(APP_URL . '/index.php?page=register');
        }
        
        $username = sanitize($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        $fullName = sanitize($_POST['full_name'] ?? '');
        $email = sanitize($_POST['email'] ?? '');
        $role = sanitize($_POST['role'] ?? 'preacher');
        
        // التحقق من صحة البيانات
        $errors = [];
        
        if (empty($username)) {
            $errors[] = 'اسم المستخدم مطلوب';
        } elseif ($this->userModel->usernameExists($username)) {
            $errors[] = 'اسم المستخدم موجود مسبقاً';
        }
        
        if (empty($password)) {
            $errors[] = 'كلمة المرور مطلوبة';
        } elseif (strlen($password) < 6) {
            $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
        
        if ($password !== $confirmPassword) {
            $errors[] = 'كلمة المرور وتأكيدها غير متطابقتين';
        }
        
        if (empty($fullName)) {
            $errors[] = 'الاسم الكامل مطلوب';
        }
        
        if (!empty($email) && !validateEmail($email)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        if (!in_array($role, ['admin', 'preacher'])) {
            $errors[] = 'نوع المستخدم غير صحيح';
        }
        
        if (!empty($errors)) {
            showMessage(implode('<br>', $errors), 'error');
            redirect(APP_URL . '/index.php?page=register');
        }
        
        // إنشاء المستخدم
        $userData = [
            'username' => $username,
            'password' => $password,
            'full_name' => $fullName,
            'email' => $email,
            'role' => $role
        ];
        
        if ($this->userModel->create($userData)) {
            showMessage('تم إنشاء المستخدم بنجاح', 'success');
            redirect(APP_URL . '/index.php?page=users');
        } else {
            showMessage('حدث خطأ أثناء إنشاء المستخدم', 'error');
            redirect(APP_URL . '/index.php?page=register');
        }
    }
    
    /**
     * عرض صفحة تغيير كلمة المرور
     */
    public function showChangePassword() {
        requireLogin();
        
        include ROOT_PATH . '/views/auth/change_password.php';
    }
    
    /**
     * معالجة تغيير كلمة المرور
     */
    public function changePassword() {
        requireLogin();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(APP_URL . '/index.php?page=change_password');
        }
        
        // التحقق من رمز CSRF
        if (!validateCSRF($_POST['csrf_token'] ?? '')) {
            showMessage('رمز الأمان غير صحيح', 'error');
            redirect(APP_URL . '/index.php?page=change_password');
        }
        
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // التحقق من صحة البيانات
        $errors = [];
        
        if (empty($currentPassword)) {
            $errors[] = 'كلمة المرور الحالية مطلوبة';
        }
        
        if (empty($newPassword)) {
            $errors[] = 'كلمة المرور الجديدة مطلوبة';
        } elseif (strlen($newPassword) < 6) {
            $errors[] = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
        }
        
        if ($newPassword !== $confirmPassword) {
            $errors[] = 'كلمة المرور الجديدة وتأكيدها غير متطابقتين';
        }
        
        if (!empty($errors)) {
            showMessage(implode('<br>', $errors), 'error');
            redirect(APP_URL . '/index.php?page=change_password');
        }
        
        // التحقق من كلمة المرور الحالية
        $user = $this->userModel->getById($_SESSION['user_id']);
        if (!$user || !verifyPassword($currentPassword, $user['password'])) {
            showMessage('كلمة المرور الحالية غير صحيحة', 'error');
            redirect(APP_URL . '/index.php?page=change_password');
        }
        
        // تحديث كلمة المرور
        $updateData = [
            'full_name' => $user['full_name'],
            'email' => $user['email'],
            'role' => $user['role'],
            'password' => $newPassword
        ];
        
        if ($this->userModel->update($_SESSION['user_id'], $updateData)) {
            showMessage('تم تغيير كلمة المرور بنجاح', 'success');
            redirect(APP_URL . '/index.php?page=dashboard');
        } else {
            showMessage('حدث خطأ أثناء تغيير كلمة المرور', 'error');
            redirect(APP_URL . '/index.php?page=change_password');
        }
    }
}
?>
