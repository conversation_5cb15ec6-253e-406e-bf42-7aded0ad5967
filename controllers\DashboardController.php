<?php
/**
 * متحكم لوحة التحكم
 * Dashboard Controller
 */

require_once ROOT_PATH . '/models/User.php';
require_once ROOT_PATH . '/models/Preacher.php';
require_once ROOT_PATH . '/models/Activity.php';
require_once ROOT_PATH . '/models/Achievement.php';
require_once ROOT_PATH . '/models/Obstacle.php';

class DashboardController {
    private $userModel;
    private $preacherModel;
    private $activityModel;
    private $achievementModel;
    private $obstacleModel;
    
    public function __construct() {
        $this->userModel = new User();
        $this->preacherModel = new Preacher();
        $this->activityModel = new Activity();
        $this->achievementModel = new Achievement();
        $this->obstacleModel = new Obstacle();
    }
    
    /**
     * عرض لوحة التحكم الرئيسية
     */
    public function index() {
        requireLogin();
        
        // الحصول على معرف الداعية للمستخدم الحالي
        $currentPreacherId = null;
        if (!isAdmin()) {
            $preacher = $this->preacherModel->getByUserId($_SESSION['user_id']);
            $currentPreacherId = $preacher ? $preacher['id'] : null;
        }
        
        // جمع الإحصائيات
        $stats = $this->getStats($currentPreacherId);
        
        // الحصول على البيانات الحديثة
        $recentData = $this->getRecentData($currentPreacherId);
        
        // تمرير البيانات إلى العرض
        $data = [
            'stats' => $stats,
            'recent_activities' => $recentData['activities'],
            'recent_achievements' => $recentData['achievements'],
            'recent_obstacles' => $recentData['obstacles'],
            'current_preacher_id' => $currentPreacherId
        ];
        
        include ROOT_PATH . '/views/dashboard/index.php';
    }
    
    /**
     * الحصول على الإحصائيات
     */
    private function getStats($preacherId = null) {
        $stats = [];
        
        if (isAdmin()) {
            // إحصائيات المشرف - جميع البيانات
            $stats['total_preachers'] = count($this->preacherModel->getAll());
            $stats['total_activities'] = count($this->activityModel->getAll());
            $stats['total_achievements'] = count($this->achievementModel->getAll());
            $stats['total_obstacles'] = count($this->obstacleModel->getAll());
            
            // إحصائيات الأنشطة
            $activityStats = $this->activityModel->getStats();
            $stats['total_attendees'] = $activityStats['total_attendees'] ?? 0;
            
            // إحصائيات الإنجازات
            $achievementStats = $this->achievementModel->getStats();
            $stats['total_beneficiaries'] = $achievementStats['total_beneficiaries'] ?? 0;
            
            // إحصائيات المعرقلات حسب الحالة
            $obstacleStats = $this->obstacleModel->getStats();
            $stats['obstacles_by_status'] = [];
            foreach ($obstacleStats['by_status'] ?? [] as $status) {
                $stats['obstacles_by_status'][$status['status']] = $status['count'];
            }
            
            // الأنشطة حسب النوع
            $stats['activities_by_type'] = [];
            foreach ($activityStats['by_type'] ?? [] as $type) {
                $stats['activities_by_type'][$type['activity_type']] = $type['count'];
            }
            
        } else {
            // إحصائيات الداعية - بياناته فقط
            if ($preacherId) {
                $stats['my_activities'] = count($this->activityModel->getAll($preacherId));
                $stats['my_achievements'] = count($this->achievementModel->getAll($preacherId));
                $stats['my_obstacles'] = count($this->obstacleModel->getAll($preacherId));
                
                // إحصائيات الأنشطة الخاصة
                $activityStats = $this->activityModel->getStats($preacherId);
                $stats['my_attendees'] = $activityStats['total_attendees'] ?? 0;
                
                // إحصائيات الإنجازات الخاصة
                $achievementStats = $this->achievementModel->getStats($preacherId);
                $stats['my_beneficiaries'] = $achievementStats['total_beneficiaries'] ?? 0;
                
                // المعرقلات حسب الحالة
                $obstacleStats = $this->obstacleModel->getStats($preacherId);
                $stats['my_obstacles_by_status'] = [];
                foreach ($obstacleStats['by_status'] ?? [] as $status) {
                    $stats['my_obstacles_by_status'][$status['status']] = $status['count'];
                }
            }
        }
        
        return $stats;
    }
    
    /**
     * الحصول على البيانات الحديثة
     */
    private function getRecentData($preacherId = null) {
        $data = [];
        
        // الأنشطة الحديثة
        $data['activities'] = $this->activityModel->getAll($preacherId, 5);
        
        // الإنجازات الحديثة
        $data['achievements'] = $this->achievementModel->getAll($preacherId, 5);
        
        // المعرقلات الحديثة
        $data['obstacles'] = $this->obstacleModel->getAll($preacherId, null, 5);
        
        return $data;
    }
    
    /**
     * عرض التقارير
     */
    public function reports() {
        requireLogin();
        
        $startDate = $_GET['start_date'] ?? date('Y-m-01'); // بداية الشهر الحالي
        $endDate = $_GET['end_date'] ?? date('Y-m-t'); // نهاية الشهر الحالي
        $preacherId = $_GET['preacher_id'] ?? null;
        
        // التحقق من الصلاحيات
        if (!isAdmin() && $preacherId) {
            $currentPreacher = $this->preacherModel->getByUserId($_SESSION['user_id']);
            if (!$currentPreacher || $currentPreacher['id'] != $preacherId) {
                showMessage('ليس لديك صلاحية لعرض هذا التقرير', 'error');
                redirect(APP_URL . '/index.php?page=dashboard');
            }
        }
        
        // إذا لم يكن مشرفاً، استخدم معرف الداعية الخاص به
        if (!isAdmin()) {
            $currentPreacher = $this->preacherModel->getByUserId($_SESSION['user_id']);
            $preacherId = $currentPreacher ? $currentPreacher['id'] : null;
        }
        
        // جمع بيانات التقرير
        $reportData = [
            'activities' => $this->activityModel->getByDateRange($startDate, $endDate, $preacherId),
            'achievements' => $this->achievementModel->getByDateRange($startDate, $endDate, $preacherId),
            'obstacles' => $this->obstacleModel->getByDateRange($startDate, $endDate, $preacherId),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'preacher_id' => $preacherId
        ];
        
        // قائمة الدعاة للمشرف
        if (isAdmin()) {
            $reportData['preachers'] = $this->preacherModel->getAll();
        }
        
        include ROOT_PATH . '/views/dashboard/reports.php';
    }
    
    /**
     * تصدير التقرير
     */
    public function exportReport() {
        requireLogin();
        
        $format = $_GET['format'] ?? 'csv';
        $startDate = $_GET['start_date'] ?? date('Y-m-01');
        $endDate = $_GET['end_date'] ?? date('Y-m-t');
        $preacherId = $_GET['preacher_id'] ?? null;
        
        // التحقق من الصلاحيات
        if (!isAdmin()) {
            $currentPreacher = $this->preacherModel->getByUserId($_SESSION['user_id']);
            $preacherId = $currentPreacher ? $currentPreacher['id'] : null;
        }
        
        // جمع البيانات
        $activities = $this->activityModel->getByDateRange($startDate, $endDate, $preacherId);
        $achievements = $this->achievementModel->getByDateRange($startDate, $endDate, $preacherId);
        $obstacles = $this->obstacleModel->getByDateRange($startDate, $endDate, $preacherId);
        
        if ($format === 'csv') {
            $this->exportCSV($activities, $achievements, $obstacles, $startDate, $endDate);
        } else {
            showMessage('تنسيق التصدير غير مدعوم', 'error');
            redirect(APP_URL . '/index.php?page=reports');
        }
    }
    
    /**
     * تصدير البيانات بصيغة CSV
     */
    private function exportCSV($activities, $achievements, $obstacles, $startDate, $endDate) {
        $filename = "report_" . $startDate . "_to_" . $endDate . ".csv";
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // إضافة BOM للدعم العربي
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // كتابة الأنشطة
        fputcsv($output, ['الأنشطة الدعوية']);
        fputcsv($output, ['اسم النشاط', 'تاريخ التنفيذ', 'نوع النشاط', 'عدد الحضور', 'اسم الداعية']);
        
        foreach ($activities as $activity) {
            fputcsv($output, [
                $activity['activity_name'],
                $activity['execution_date'],
                $activity['activity_type'],
                $activity['attendees_count'],
                $activity['preacher_name']
            ]);
        }
        
        // كتابة الإنجازات
        fputcsv($output, []);
        fputcsv($output, ['الإنجازات']);
        fputcsv($output, ['وصف الإنجاز', 'تاريخ الإنجاز', 'عدد المستفيدين', 'اسم الداعية']);
        
        foreach ($achievements as $achievement) {
            fputcsv($output, [
                $achievement['achievement_description'],
                $achievement['achievement_date'],
                $achievement['beneficiaries_count'],
                $achievement['preacher_name']
            ]);
        }
        
        // كتابة المعرقلات
        fputcsv($output, []);
        fputcsv($output, ['المعرقلات']);
        fputcsv($output, ['وصف المشكلة', 'تاريخ المشكلة', 'الجهة المسؤولة', 'الحالة', 'اسم الداعية']);
        
        foreach ($obstacles as $obstacle) {
            fputcsv($output, [
                $obstacle['problem_description'],
                $obstacle['problem_date'],
                $obstacle['responsible_entity'],
                $obstacle['status'],
                $obstacle['preacher_name']
            ]);
        }
        
        fclose($output);
        exit;
    }
}
?>
