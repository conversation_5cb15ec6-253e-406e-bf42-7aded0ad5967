<?php
/**
 * إدارة الجلسات
 * Session Management
 */

/**
 * دالة بدء الجلسة الآمنة
 */
function startSecureSession() {
    // إعدادات الجلسة الآمنة
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', 0); // تغيير إلى 1 عند استخدام HTTPS
    
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // التحقق من انتهاء صلاحية الجلسة
    if (isset($_SESSION['last_activity']) && 
        (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
        destroySession();
        return false;
    }
    
    $_SESSION['last_activity'] = time();
    return true;
}

/**
 * دالة تسجيل دخول المستخدم
 */
function loginUser($user) {
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['full_name'] = $user['full_name'];
    $_SESSION['user_role'] = $user['role'];
    $_SESSION['login_time'] = time();
    $_SESSION['last_activity'] = time();
    
    // إنشاء رمز CSRF
    $_SESSION['csrf_token'] = generateToken(16);
}

/**
 * دالة تسجيل خروج المستخدم
 */
function logoutUser() {
    destroySession();
}

/**
 * دالة تدمير الجلسة
 */
function destroySession() {
    if (session_status() === PHP_SESSION_ACTIVE) {
        session_unset();
        session_destroy();
        
        // حذف ملف تعريف الارتباط
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
    }
}

/**
 * دالة التحقق من رمز CSRF
 */
function validateCSRF($token) {
    return isset($_SESSION['csrf_token']) && 
           hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * دالة الحصول على رمز CSRF
 */
function getCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateToken(16);
    }
    return $_SESSION['csrf_token'];
}

/**
 * دالة التحقق من صلاحية الوصول للصفحة
 */
function requireLogin() {
    if (!isLoggedIn()) {
        redirect(APP_URL . '/index.php?page=login');
    }
}

/**
 * دالة التحقق من صلاحيات المشرف
 */
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        showMessage('ليس لديك صلاحية للوصول لهذه الصفحة', 'error');
        redirect(APP_URL . '/index.php?page=dashboard');
    }
}

/**
 * دالة التحقق من ملكية البيانات للمستخدم
 */
function checkOwnership($preacherId) {
    if (isAdmin()) {
        return true; // المشرف يمكنه الوصول لكل شيء
    }
    
    // التحقق من أن الداعية يملك هذه البيانات
    $db = Database::getInstance()->getConnection();
    $stmt = $db->prepare("SELECT user_id FROM preachers WHERE id = ?");
    $stmt->execute([$preacherId]);
    $preacher = $stmt->fetch();
    
    return $preacher && $preacher['user_id'] == $_SESSION['user_id'];
}

// بدء الجلسة الآمنة
startSecureSession();
?>
