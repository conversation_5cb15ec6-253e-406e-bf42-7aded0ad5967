<?php
/**
 * الملف الرئيسي لتطبيق إدارة شؤون الدعاة
 * Main Application File - Da'wah Management System
 */

// تفعيل عرض الأخطاء للتشخيص
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>اختبار النظام</h1>";
echo "<p>PHP يعمل بشكل صحيح!</p>";
echo "<p>التاريخ: " . date('Y-m-d H:i:s') . "</p>";

// اختبار تضمين الملفات تدريجياً
echo "<h2>اختبار تضمين الملفات:</h2>";

try {
    echo "<p>1. اختبار config/database.php...</p>";
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        echo "<p style='color: green;'>✓ تم تضمين config/database.php بنجاح</p>";
    } else {
        echo "<p style='color: red;'>✗ ملف config/database.php غير موجود</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في config/database.php: " . $e->getMessage() . "</p>";
}

try {
    echo "<p>2. اختبار config/config.php...</p>";
    if (file_exists('config/config.php')) {
        require_once 'config/config.php';
        echo "<p style='color: green;'>✓ تم تضمين config/config.php بنجاح</p>";
    } else {
        echo "<p style='color: red;'>✗ ملف config/config.php غير موجود</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في config/config.php: " . $e->getMessage() . "</p>";
}

echo "<h2>روابط الاختبار:</h2>";
echo "<ul>";
echo "<li><a href='hello.php'>اختبار PHP البسيط</a></li>";
echo "<li><a href='info.php'>معلومات PHP</a></li>";
echo "<li><a href='simple_test.php'>اختبار النظام البسيط</a></li>";
echo "<li><a href='debug.php'>تشخيص متقدم</a></li>";
echo "</ul>";

// إيقاف التنفيذ هنا للاختبار
exit;

// تضمين المتحكمات
require_once ROOT_PATH . '/controllers/AuthController.php';
require_once ROOT_PATH . '/controllers/DashboardController.php';
require_once ROOT_PATH . '/controllers/PreacherController.php';

// الحصول على الصفحة والإجراء المطلوب
$page = $_GET['page'] ?? 'dashboard';
$action = $_GET['action'] ?? 'index';
$id = $_GET['id'] ?? null;

// التوجيه إلى صفحة تسجيل الدخول إذا لم يكن المستخدم مسجل دخوله
if (!isLoggedIn() && $page !== 'login') {
    redirect(APP_URL . '/index.php?page=login');
}

// إنشاء مثيل من المتحكم المناسب وتنفيذ الإجراء
try {
    switch ($page) {
        case 'login':
            $controller = new AuthController();
            if ($action === 'login') {
                $controller->login();
            } else {
                $controller->showLogin();
            }
            break;

        case 'logout':
            $controller = new AuthController();
            $controller->logout();
            break;

        case 'register':
            $controller = new AuthController();
            if ($action === 'register') {
                $controller->register();
            } else {
                $controller->showRegister();
            }
            break;

        case 'change_password':
            $controller = new AuthController();
            if ($action === 'change') {
                $controller->changePassword();
            } else {
                $controller->showChangePassword();
            }
            break;

        case 'dashboard':
            $controller = new DashboardController();
            if ($action === 'reports') {
                $controller->reports();
            } elseif ($action === 'export') {
                $controller->exportReport();
            } else {
                $controller->index();
            }
            break;

        case 'preachers':
            $controller = new PreacherController();
            switch ($action) {
                case 'create':
                    $controller->create();
                    break;
                case 'store':
                    $controller->store();
                    break;
                case 'show':
                    if ($id) {
                        $controller->show($id);
                    } else {
                        redirect(APP_URL . '/index.php?page=preachers');
                    }
                    break;
                case 'edit':
                    if ($id) {
                        $controller->edit($id);
                    } else {
                        redirect(APP_URL . '/index.php?page=preachers');
                    }
                    break;
                case 'update':
                    if ($id) {
                        $controller->update($id);
                    } else {
                        redirect(APP_URL . '/index.php?page=preachers');
                    }
                    break;
                case 'delete':
                    if ($id) {
                        $controller->delete($id);
                    } else {
                        redirect(APP_URL . '/index.php?page=preachers');
                    }
                    break;
                case 'toggle_active':
                    if ($id) {
                        $controller->toggleActive($id);
                    } else {
                        redirect(APP_URL . '/index.php?page=preachers');
                    }
                    break;
                default:
                    $controller->index();
                    break;
            }
            break;

        case 'activities':
            // سيتم إضافة متحكم الأنشطة لاحقاً
            showMessage('صفحة الأنشطة قيد التطوير', 'info');
            redirect(APP_URL . '/index.php?page=dashboard');
            break;

        case 'achievements':
            // سيتم إضافة متحكم الإنجازات لاحقاً
            showMessage('صفحة الإنجازات قيد التطوير', 'info');
            redirect(APP_URL . '/index.php?page=dashboard');
            break;

        case 'obstacles':
            // سيتم إضافة متحكم المعرقلات لاحقاً
            showMessage('صفحة المعرقلات قيد التطوير', 'info');
            redirect(APP_URL . '/index.php?page=dashboard');
            break;

        default:
            // صفحة غير موجودة - إعادة توجيه إلى لوحة التحكم
            redirect(APP_URL . '/index.php?page=dashboard');
            break;
    }

} catch (Exception $e) {
    // تسجيل الخطأ
    error_log("خطأ في التطبيق: " . $e->getMessage());

    // عرض رسالة خطأ للمستخدم
    showMessage('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.', 'error');

    // إعادة توجيه إلى لوحة التحكم
    if (isLoggedIn()) {
        redirect(APP_URL . '/index.php?page=dashboard');
    } else {
        redirect(APP_URL . '/index.php?page=login');
    }
}
?>
