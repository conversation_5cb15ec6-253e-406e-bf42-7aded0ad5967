<?php
/**
 * نموذج المعرقل
 * Obstacle Model
 */

class Obstacle {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * إنشاء معرقل جديد
     */
    public function create($data) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO obstacles (preacher_id, problem_description, problem_date, 
                                     responsible_entity, status, solution_description, notes, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            return $stmt->execute([
                $data['preacher_id'],
                $data['problem_description'],
                $data['problem_date'],
                $data['responsible_entity'] ?? null,
                $data['status'] ?? 'مفتوح',
                $data['solution_description'] ?? null,
                $data['notes'] ?? null,
                $_SESSION['user_id']
            ]);
        } catch (PDOException $e) {
            error_log("خطأ في إنشاء المعرقل: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على معرقل بواسطة المعرف
     */
    public function getById($id) {
        try {
            $stmt = $this->db->prepare("
                SELECT o.*, p.full_name as preacher_name, u.full_name as created_by_name
                FROM obstacles o 
                LEFT JOIN preachers p ON o.preacher_id = p.id 
                LEFT JOIN users u ON o.created_by = u.id 
                WHERE o.id = ?
            ");
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("خطأ في جلب المعرقل: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على جميع المعرقلات
     */
    public function getAll($preacherId = null, $status = null, $limit = null) {
        try {
            $sql = "
                SELECT o.*, p.full_name as preacher_name, u.full_name as created_by_name
                FROM obstacles o 
                LEFT JOIN preachers p ON o.preacher_id = p.id 
                LEFT JOIN users u ON o.created_by = u.id
            ";
            
            $params = [];
            $conditions = [];
            
            if ($preacherId) {
                $conditions[] = "o.preacher_id = ?";
                $params[] = $preacherId;
            }
            
            if ($status) {
                $conditions[] = "o.status = ?";
                $params[] = $status;
            }
            
            if (!empty($conditions)) {
                $sql .= " WHERE " . implode(" AND ", $conditions);
            }
            
            $sql .= " ORDER BY o.problem_date DESC";
            
            if ($limit) {
                $sql .= " LIMIT ?";
                $params[] = $limit;
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في جلب المعرقلات: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث المعرقل
     */
    public function update($id, $data) {
        try {
            $stmt = $this->db->prepare("
                UPDATE obstacles 
                SET problem_description = ?, problem_date = ?, responsible_entity = ?, 
                    status = ?, solution_description = ?, notes = ?
                WHERE id = ?
            ");
            
            return $stmt->execute([
                $data['problem_description'],
                $data['problem_date'],
                $data['responsible_entity'] ?? null,
                $data['status'] ?? 'مفتوح',
                $data['solution_description'] ?? null,
                $data['notes'] ?? null,
                $id
            ]);
        } catch (PDOException $e) {
            error_log("خطأ في تحديث المعرقل: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * حذف المعرقل
     */
    public function delete($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM obstacles WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("خطأ في حذف المعرقل: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديث حالة المعرقل
     */
    public function updateStatus($id, $status, $solution = null) {
        try {
            $stmt = $this->db->prepare("
                UPDATE obstacles 
                SET status = ?, solution_description = ? 
                WHERE id = ?
            ");
            
            return $stmt->execute([$status, $solution, $id]);
        } catch (PDOException $e) {
            error_log("خطأ في تحديث حالة المعرقل: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * البحث في المعرقلات
     */
    public function search($keyword, $preacherId = null) {
        try {
            $sql = "
                SELECT o.*, p.full_name as preacher_name, u.full_name as created_by_name
                FROM obstacles o 
                LEFT JOIN preachers p ON o.preacher_id = p.id 
                LEFT JOIN users u ON o.created_by = u.id 
                WHERE (o.problem_description LIKE ? OR o.responsible_entity LIKE ? OR o.notes LIKE ?)
            ";
            
            $params = ["%$keyword%", "%$keyword%", "%$keyword%"];
            
            if ($preacherId) {
                $sql .= " AND o.preacher_id = ?";
                $params[] = $preacherId;
            }
            
            $sql .= " ORDER BY o.problem_date DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في البحث في المعرقلات: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على المعرقلات حسب التاريخ
     */
    public function getByDateRange($startDate, $endDate, $preacherId = null) {
        try {
            $sql = "
                SELECT o.*, p.full_name as preacher_name, u.full_name as created_by_name
                FROM obstacles o 
                LEFT JOIN preachers p ON o.preacher_id = p.id 
                LEFT JOIN users u ON o.created_by = u.id 
                WHERE o.problem_date BETWEEN ? AND ?
            ";
            
            $params = [$startDate, $endDate];
            
            if ($preacherId) {
                $sql .= " AND o.preacher_id = ?";
                $params[] = $preacherId;
            }
            
            $sql .= " ORDER BY o.problem_date DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في جلب المعرقلات حسب التاريخ: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات المعرقلات
     */
    public function getStats($preacherId = null) {
        try {
            $stats = [];
            
            $whereClause = $preacherId ? "WHERE preacher_id = ?" : "";
            $params = $preacherId ? [$preacherId] : [];
            
            // إجمالي المعرقلات
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM obstacles $whereClause");
            $stmt->execute($params);
            $stats['total_obstacles'] = $stmt->fetchColumn();
            
            // المعرقلات حسب الحالة
            $stmt = $this->db->prepare("
                SELECT status, COUNT(*) as count 
                FROM obstacles $whereClause 
                GROUP BY status
            ");
            $stmt->execute($params);
            $stats['by_status'] = $stmt->fetchAll();
            
            // المعرقلات الشهرية
            $stmt = $this->db->prepare("
                SELECT DATE_FORMAT(problem_date, '%Y-%m') as month, COUNT(*) as count 
                FROM obstacles $whereClause 
                GROUP BY month 
                ORDER BY month DESC 
                LIMIT 12
            ");
            $stmt->execute($params);
            $stats['monthly'] = $stmt->fetchAll();
            
            return $stats;
        } catch (PDOException $e) {
            error_log("خطأ في جلب إحصائيات المعرقلات: " . $e->getMessage());
            return [];
        }
    }
}
?>
