<?php
/**
 * نموذج الداعية
 * Preacher Model
 */

class Preacher {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * إنشاء داعية جديد
     */
    public function create($data) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO preachers (user_id, full_name, email, phone, address,
                                     specialization, education, experience_years,
                                     languages, bio)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            return $stmt->execute([
                $data['user_id'] ?? null,
                $data['full_name'],
                $data['email'] ?? '',
                $data['phone'] ?? '',
                $data['address'] ?? '',
                $data['specialization'] ?? '',
                $data['education'] ?? '',
                $data['experience_years'] ?? 0,
                $data['languages'] ?? '',
                $data['bio'] ?? ''
            ]);
        } catch (PDOException $e) {
            error_log("خطأ في إنشاء الداعية: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على داعية بواسطة المعرف
     */
    public function getById($id) {
        try {
            $stmt = $this->db->prepare("
                SELECT p.*, u.username, u.role
                FROM preachers p
                LEFT JOIN users u ON p.user_id = u.id
                WHERE p.id = ?
            ");
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("خطأ في جلب الداعية: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على جميع الدعاة
     */
    public function getAll($activeOnly = true) {
        try {
            $sql = "
                SELECT p.*, u.username, u.role
                FROM preachers p
                LEFT JOIN users u ON p.user_id = u.id
            ";

            if ($activeOnly) {
                $sql .= " WHERE p.is_active = 1";
            }

            $sql .= " ORDER BY p.created_at DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في جلب الدعاة: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على داعية بواسطة معرف المستخدم
     */
    public function getByUserId($userId) {
        try {
            $stmt = $this->db->prepare("
                SELECT p.*, u.username, u.role
                FROM preachers p
                LEFT JOIN users u ON p.user_id = u.id
                WHERE p.user_id = ?
            ");
            $stmt->execute([$userId]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("خطأ في جلب الداعية بواسطة معرف المستخدم: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تحديث بيانات الداعية
     */
    public function update($id, $data) {
        try {
            $stmt = $this->db->prepare("
                UPDATE preachers
                SET full_name = ?, email = ?, phone = ?, address = ?,
                    specialization = ?, education = ?, experience_years = ?,
                    languages = ?, bio = ?
                WHERE id = ?
            ");

            return $stmt->execute([
                $data['full_name'],
                $data['email'] ?? '',
                $data['phone'] ?? '',
                $data['address'] ?? '',
                $data['specialization'] ?? '',
                $data['education'] ?? '',
                $data['experience_years'] ?? 0,
                $data['languages'] ?? '',
                $data['bio'] ?? '',
                $id
            ]);
        } catch (PDOException $e) {
            error_log("خطأ في تحديث الداعية: " . $e->getMessage());
            return false;
        }
    }

    /**
     * حذف داعية
     */
    public function delete($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM preachers WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("خطأ في حذف الداعية: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تفعيل/إلغاء تفعيل الداعية
     */
    public function toggleActive($id) {
        try {
            $stmt = $this->db->prepare("
                UPDATE preachers
                SET is_active = CASE WHEN is_active = 1 THEN 0 ELSE 1 END
                WHERE id = ?
            ");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("خطأ في تغيير حالة الداعية: " . $e->getMessage());
            return false;
        }
    }

    /**
     * البحث في الدعاة
     */
    public function search($keyword) {
        try {
            $stmt = $this->db->prepare("
                SELECT p.*, u.username, u.role
                FROM preachers p
                LEFT JOIN users u ON p.user_id = u.id
                WHERE p.full_name LIKE ?
                   OR p.nationality LIKE ?
                   OR p.email LIKE ?
                   OR p.specialization LIKE ?
                ORDER BY p.created_at DESC
            ");

            $searchTerm = "%$keyword%";
            $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في البحث في الدعاة: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على إحصائيات الداعية
     */
    public function getStats($preacherId) {
        try {
            $stats = [];

            // عدد الأنشطة
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM activities WHERE preacher_id = ?");
            $stmt->execute([$preacherId]);
            $stats['activities_count'] = $stmt->fetchColumn();

            // عدد الإنجازات
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM achievements WHERE preacher_id = ?");
            $stmt->execute([$preacherId]);
            $stats['achievements_count'] = $stmt->fetchColumn();

            // عدد المعرقلات
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM obstacles WHERE preacher_id = ?");
            $stmt->execute([$preacherId]);
            $stats['obstacles_count'] = $stmt->fetchColumn();

            // إجمالي الحضور
            $stmt = $this->db->prepare("SELECT SUM(attendees_count) FROM activities WHERE preacher_id = ?");
            $stmt->execute([$preacherId]);
            $stats['total_attendees'] = $stmt->fetchColumn() ?? 0;

            // إجمالي المستفيدين
            $stmt = $this->db->prepare("SELECT SUM(beneficiaries_count) FROM achievements WHERE preacher_id = ?");
            $stmt->execute([$preacherId]);
            $stats['total_beneficiaries'] = $stmt->fetchColumn() ?? 0;

            return $stats;
        } catch (PDOException $e) {
            error_log("خطأ في جلب إحصائيات الداعية: " . $e->getMessage());
            return [];
        }
    }
}
?>
