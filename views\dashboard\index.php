<?php
$pageTitle = 'لوحة التحكم - ' . APP_NAME;
include ROOT_PATH . '/views/layouts/header.php';
?>

<div class="main-content">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة التحكم
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item active">الرئيسية</li>
                    </ol>
                </nav>
            </div>
            <div class="col-auto">
                <span class="badge bg-light text-dark">
                    <i class="fas fa-calendar me-1"></i>
                    <?php echo formatDateArabic(date('Y-m-d')); ?>
                </span>
            </div>
        </div>
    </div>

    <!-- Welcome Message -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-success border-0" role="alert">
                <h4 class="alert-heading">
                    <i class="fas fa-hand-peace me-2"></i>
                    مرحباً <?php echo $_SESSION['full_name']; ?>
                </h4>
                <p class="mb-0">
                    <?php if (isAdmin()): ?>
                        مرحباً بك في نظام إدارة شؤون الدعاة. يمكنك من هنا إدارة جميع البيانات والتقارير.
                    <?php else: ?>
                        مرحباً بك في نظام إدارة شؤون الدعاة. يمكنك من هنا إدارة أنشطتك وإنجازاتك.
                    <?php endif; ?>
                </p>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <?php if (isAdmin()): ?>
            <!-- Admin Statistics -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي الدعاة
                                </div>
                                <div class="stats-number"><?php echo $data['stats']['total_preachers'] ?? 0; ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    إجمالي الأنشطة
                                </div>
                                <div class="stats-number"><?php echo $data['stats']['total_activities'] ?? 0; ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-calendar-alt fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    إجمالي الإنجازات
                                </div>
                                <div class="stats-number"><?php echo $data['stats']['total_achievements'] ?? 0; ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-trophy fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    إجمالي المعرقلات
                                </div>
                                <div class="stats-number"><?php echo $data['stats']['total_obstacles'] ?? 0; ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Admin Stats -->
            <div class="col-xl-6 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    إجمالي الحضور
                                </div>
                                <div class="stats-number"><?php echo number_format($data['stats']['total_attendees'] ?? 0); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-user-friends fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-6 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    إجمالي المستفيدين
                                </div>
                                <div class="stats-number"><?php echo number_format($data['stats']['total_beneficiaries'] ?? 0); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-heart fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <?php else: ?>
            <!-- Preacher Statistics -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    أنشطتي
                                </div>
                                <div class="stats-number"><?php echo $data['stats']['my_activities'] ?? 0; ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-calendar-alt fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    إنجازاتي
                                </div>
                                <div class="stats-number"><?php echo $data['stats']['my_achievements'] ?? 0; ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-trophy fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    معرقلاتي
                                </div>
                                <div class="stats-number"><?php echo $data['stats']['my_obstacles'] ?? 0; ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي الحضور
                                </div>
                                <div class="stats-number"><?php echo number_format($data['stats']['my_attendees'] ?? 0); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-user-friends fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Recent Data -->
    <div class="row">
        <!-- Recent Activities -->
        <div class="col-xl-4 col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-calendar-alt me-2"></i>
                        الأنشطة الحديثة
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($data['recent_activities'])): ?>
                        <?php foreach (array_slice($data['recent_activities'], 0, 5) as $activity): ?>
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-success rounded-circle p-2">
                                        <i class="fas fa-calendar text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($activity['activity_name']); ?></h6>
                                    <small class="text-muted">
                                        <?php echo formatDateArabic($activity['execution_date']); ?>
                                        <?php if (isAdmin()): ?>
                                            - <?php echo htmlspecialchars($activity['preacher_name']); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="text-center">
                            <a href="<?php echo APP_URL; ?>/index.php?page=activities" class="btn btn-sm btn-outline-success">
                                عرض الكل
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted">
                            <i class="fas fa-calendar-times fa-3x mb-3"></i>
                            <p>لا توجد أنشطة حديثة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Achievements -->
        <div class="col-xl-4 col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-trophy me-2"></i>
                        الإنجازات الحديثة
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($data['recent_achievements'])): ?>
                        <?php foreach (array_slice($data['recent_achievements'], 0, 5) as $achievement): ?>
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-info rounded-circle p-2">
                                        <i class="fas fa-trophy text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1"><?php echo truncateText($achievement['achievement_description'], 50); ?></h6>
                                    <small class="text-muted">
                                        <?php echo formatDateArabic($achievement['achievement_date']); ?>
                                        <?php if (isAdmin()): ?>
                                            - <?php echo htmlspecialchars($achievement['preacher_name']); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="text-center">
                            <a href="<?php echo APP_URL; ?>/index.php?page=achievements" class="btn btn-sm btn-outline-info">
                                عرض الكل
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted">
                            <i class="fas fa-trophy fa-3x mb-3"></i>
                            <p>لا توجد إنجازات حديثة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Obstacles -->
        <div class="col-xl-4 col-lg-12 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        المعرقلات الحديثة
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($data['recent_obstacles'])): ?>
                        <?php foreach (array_slice($data['recent_obstacles'], 0, 5) as $obstacle): ?>
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-warning rounded-circle p-2">
                                        <i class="fas fa-exclamation text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1"><?php echo truncateText($obstacle['problem_description'], 50); ?></h6>
                                    <small class="text-muted">
                                        <?php echo formatDateArabic($obstacle['problem_date']); ?>
                                        <?php if (isAdmin()): ?>
                                            - <?php echo htmlspecialchars($obstacle['preacher_name']); ?>
                                        <?php endif; ?>
                                    </small>
                                    <br>
                                    <span class="badge bg-<?php echo $obstacle['status'] === 'محلول' ? 'success' : ($obstacle['status'] === 'قيد المعالجة' ? 'warning' : 'danger'); ?>">
                                        <?php echo $obstacle['status']; ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="text-center">
                            <a href="<?php echo APP_URL; ?>/index.php?page=obstacles" class="btn btn-sm btn-outline-warning">
                                عرض الكل
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <p>لا توجد معرقلات حديثة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php if (isAdmin()): ?>
                            <div class="col-md-3 mb-3">
                                <a href="<?php echo APP_URL; ?>/index.php?page=preachers&action=create" class="btn btn-primary w-100">
                                    <i class="fas fa-user-plus fa-2x d-block mb-2"></i>
                                    إضافة داعية جديد
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo APP_URL; ?>/index.php?page=activities&action=create" class="btn btn-success w-100">
                                <i class="fas fa-plus-circle fa-2x d-block mb-2"></i>
                                إضافة نشاط
                            </a>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo APP_URL; ?>/index.php?page=achievements&action=create" class="btn btn-info w-100">
                                <i class="fas fa-trophy fa-2x d-block mb-2"></i>
                                إضافة إنجاز
                            </a>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo APP_URL; ?>/index.php?page=dashboard&action=reports" class="btn btn-warning w-100">
                                <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                                عرض التقارير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include ROOT_PATH . '/views/layouts/footer.php'; ?>
