<?php
$pageTitle = 'إضافة داعية جديد - ' . APP_NAME;
include ROOT_PATH . '/views/layouts/header.php';
?>

<div class="main-content">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة داعية جديد
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="<?php echo APP_URL; ?>/index.php?page=dashboard">الرئيسية</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo APP_URL; ?>/index.php?page=preachers">الدعاة</a>
                        </li>
                        <li class="breadcrumb-item active">إضافة جديد</li>
                    </ol>
                </nav>
            </div>
            <div class="col-auto">
                <a href="<?php echo APP_URL; ?>/index.php?page=preachers" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Form Card -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-edit me-2"></i>
                بيانات الداعية الجديد
            </h6>
        </div>
        <div class="card-body">
            <form method="POST" action="<?php echo APP_URL; ?>/index.php?page=preachers&action=store" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo getCSRFToken(); ?>">
                
                <!-- Personal Information Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="text-primary border-bottom pb-2 mb-3">
                            <i class="fas fa-user me-2"></i>
                            المعلومات الشخصية
                        </h5>
                    </div>
                </div>

                <div class="row">
                    <!-- Full Name -->
                    <div class="col-md-6 mb-3">
                        <label for="full_name" class="form-label">
                            <i class="fas fa-user me-2"></i>
                            الاسم الكامل <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               class="form-control" 
                               id="full_name" 
                               name="full_name" 
                               required
                               placeholder="أدخل الاسم الكامل"
                               maxlength="100">
                        <div class="invalid-feedback">
                            يرجى إدخال الاسم الكامل
                        </div>
                    </div>

                    <!-- Nationality -->
                    <div class="col-md-6 mb-3">
                        <label for="nationality" class="form-label">
                            <i class="fas fa-flag me-2"></i>
                            الجنسية <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="nationality" name="nationality" required>
                            <option value="">اختر الجنسية</option>
                            <?php foreach ($data['nationalities'] as $nationality): ?>
                                <option value="<?php echo htmlspecialchars($nationality); ?>">
                                    <?php echo htmlspecialchars($nationality); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback">
                            يرجى اختيار الجنسية
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Language -->
                    <div class="col-md-6 mb-3">
                        <label for="language" class="form-label">
                            <i class="fas fa-language me-2"></i>
                            اللغة <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="language" name="language" required>
                            <option value="">اختر اللغة</option>
                            <?php foreach ($data['languages'] as $language): ?>
                                <option value="<?php echo htmlspecialchars($language); ?>">
                                    <?php echo htmlspecialchars($language); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback">
                            يرجى اختيار اللغة
                        </div>
                    </div>

                    <!-- Mobile Phone -->
                    <div class="col-md-6 mb-3">
                        <label for="mobile_phone" class="form-label">
                            <i class="fas fa-phone me-2"></i>
                            رقم الجوال
                        </label>
                        <input type="tel" 
                               class="form-control" 
                               id="mobile_phone" 
                               name="mobile_phone" 
                               placeholder="مثال: +966501234567"
                               pattern="[\+]?[0-9\-\(\)\s]+"
                               maxlength="20">
                        <div class="invalid-feedback">
                            يرجى إدخال رقم جوال صحيح
                        </div>
                        <small class="form-text text-muted">
                            مثال: +966501234567 أو 0501234567
                        </small>
                    </div>
                </div>

                <!-- Contact Information Section -->
                <div class="row mb-4 mt-4">
                    <div class="col-12">
                        <h5 class="text-primary border-bottom pb-2 mb-3">
                            <i class="fas fa-address-book me-2"></i>
                            معلومات التواصل
                        </h5>
                    </div>
                </div>

                <div class="row">
                    <!-- Email -->
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-2"></i>
                            البريد الإلكتروني
                        </label>
                        <input type="email" 
                               class="form-control" 
                               id="email" 
                               name="email" 
                               placeholder="<EMAIL>"
                               maxlength="100">
                        <div class="invalid-feedback">
                            يرجى إدخال بريد إلكتروني صحيح
                        </div>
                    </div>

                    <!-- User Account -->
                    <div class="col-md-6 mb-3">
                        <label for="user_id" class="form-label">
                            <i class="fas fa-user-tag me-2"></i>
                            ربط بحساب مستخدم
                        </label>
                        <select class="form-select" id="user_id" name="user_id">
                            <option value="">بدون ربط</option>
                            <!-- سيتم إضافة قائمة المستخدمين هنا لاحقاً -->
                        </select>
                        <small class="form-text text-muted">
                            اختياري: يمكن ربط الداعية بحساب مستخدم للدخول للنظام
                        </small>
                    </div>
                </div>

                <!-- Professional Information Section -->
                <div class="row mb-4 mt-4">
                    <div class="col-12">
                        <h5 class="text-primary border-bottom pb-2 mb-3">
                            <i class="fas fa-graduation-cap me-2"></i>
                            المعلومات المهنية
                        </h5>
                    </div>
                </div>

                <div class="row">
                    <!-- Specialization -->
                    <div class="col-12 mb-3">
                        <label for="specialization" class="form-label">
                            <i class="fas fa-book me-2"></i>
                            التخصص الدعوي
                        </label>
                        <textarea class="form-control" 
                                  id="specialization" 
                                  name="specialization" 
                                  rows="3"
                                  placeholder="مثال: الفقه والعقيدة، التفسير، الحديث، الدعوة والإرشاد..."
                                  maxlength="500"></textarea>
                        <small class="form-text text-muted">
                            اذكر مجالات التخصص الدعوي للداعية
                        </small>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="row mb-4 mt-4">
                    <div class="col-12">
                        <h5 class="text-primary border-bottom pb-2 mb-3">
                            <i class="fas fa-sticky-note me-2"></i>
                            معلومات إضافية
                        </h5>
                    </div>
                </div>

                <div class="row">
                    <!-- Notes -->
                    <div class="col-12 mb-3">
                        <label for="notes" class="form-label">
                            <i class="fas fa-comment me-2"></i>
                            ملاحظات
                        </label>
                        <textarea class="form-control" 
                                  id="notes" 
                                  name="notes" 
                                  rows="4"
                                  placeholder="أي ملاحظات إضافية عن الداعية..."
                                  maxlength="1000"></textarea>
                        <small class="form-text text-muted">
                            ملاحظات عامة أو معلومات إضافية مهمة
                        </small>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <hr>
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ البيانات
                                </button>
                                <button type="reset" class="btn btn-outline-secondary btn-lg ms-2">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                            <div>
                                <a href="<?php echo APP_URL; ?>/index.php?page=preachers" class="btn btn-outline-danger btn-lg">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Help Card -->
    <div class="card mt-4">
        <div class="card-header bg-info text-white">
            <h6 class="m-0">
                <i class="fas fa-info-circle me-2"></i>
                إرشادات الإدخال
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-info">الحقول المطلوبة:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>الاسم الكامل</li>
                        <li><i class="fas fa-check text-success me-2"></i>الجنسية</li>
                        <li><i class="fas fa-check text-success me-2"></i>اللغة</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-info">نصائح مهمة:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-lightbulb text-warning me-2"></i>تأكد من صحة رقم الجوال</li>
                        <li><i class="fas fa-lightbulb text-warning me-2"></i>البريد الإلكتروني يجب أن يكون صحيحاً</li>
                        <li><i class="fas fa-lightbulb text-warning me-2"></i>يمكن تعديل البيانات لاحقاً</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحسين تجربة المستخدم للنموذج
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة النموذج
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        form.classList.add('was-validated');
    });
    
    // تحسين حقل رقم الجوال
    const phoneInput = document.getElementById('mobile_phone');
    phoneInput.addEventListener('input', function() {
        // إزالة الأحرف غير المسموحة
        this.value = this.value.replace(/[^0-9\+\-\(\)\s]/g, '');
    });
    
    // تحسين حقل البريد الإلكتروني
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('blur', function() {
        if (this.value && !this.value.includes('@')) {
            this.setCustomValidity('يرجى إدخال بريد إلكتروني صحيح');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // عداد الأحرف للحقول النصية
    const textareas = document.querySelectorAll('textarea[maxlength]');
    textareas.forEach(textarea => {
        const maxLength = textarea.getAttribute('maxlength');
        const counter = document.createElement('small');
        counter.className = 'form-text text-muted text-end';
        counter.style.display = 'block';
        textarea.parentNode.appendChild(counter);
        
        function updateCounter() {
            const remaining = maxLength - textarea.value.length;
            counter.textContent = `${remaining} حرف متبقي`;
            
            if (remaining < 50) {
                counter.className = 'form-text text-warning text-end';
            } else {
                counter.className = 'form-text text-muted text-end';
            }
        }
        
        textarea.addEventListener('input', updateCounter);
        updateCounter();
    });
});
</script>

<?php include ROOT_PATH . '/views/layouts/footer.php'; ?>
