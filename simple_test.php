<?php
/**
 * اختبار بسيط للنظام
 * Simple System Test
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط - نظام إدارة شؤون الدعاة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>اختبار بسيط للنظام</h1>
    
    <h2>1. معلومات PHP</h2>
    <p>إصدار PHP: <span class="info"><?php echo PHP_VERSION; ?></span></p>
    <p>امتداد PDO: <span class="<?php echo extension_loaded('pdo') ? 'success' : 'error'; ?>">
        <?php echo extension_loaded('pdo') ? 'مثبت ✓' : 'غير مثبت ✗'; ?>
    </span></p>
    
    <h2>2. اختبار الملفات</h2>
    <?php
    $files = [
        'config/database.php' => 'ملف قاعدة البيانات',
        'config/config.php' => 'ملف الإعدادات',
        'index.php' => 'الملف الرئيسي'
    ];
    
    foreach ($files as $file => $description) {
        $exists = file_exists($file);
        echo "<p>$description: <span class='" . ($exists ? 'success' : 'error') . "'>";
        echo $exists ? 'موجود ✓' : 'غير موجود ✗';
        echo "</span></p>";
    }
    ?>
    
    <h2>3. اختبار قاعدة البيانات</h2>
    <?php
    try {
        // إعدادات قاعدة البيانات المباشرة
        $host = 'localhost';
        $dbname = 'dawah_management';
        $username = 'root';
        $password = '';
        
        $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        echo "<p class='success'>الاتصال بقاعدة البيانات: نجح ✓</p>";
        
        // اختبار الجداول
        $tables = ['users', 'preachers', 'activities', 'achievements', 'obstacles'];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            $exists = $stmt->rowCount() > 0;
            echo "<p>جدول $table: <span class='" . ($exists ? 'success' : 'error') . "'>";
            echo $exists ? 'موجود ✓' : 'غير موجود ✗';
            echo "</span></p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>خطأ في قاعدة البيانات: " . $e->getMessage() . " ✗</p>";
        echo "<p class='info'>تأكد من:</p>";
        echo "<ul>";
        echo "<li>تشغيل خادم MySQL</li>";
        echo "<li>إنشاء قاعدة البيانات 'dawah_management'</li>";
        echo "<li>استيراد ملف sql/database.sql</li>";
        echo "</ul>";
    }
    ?>
    
    <h2>4. الخطوات التالية</h2>
    <p>إذا كانت جميع الاختبارات ناجحة، جرب:</p>
    <ul>
        <li><a href="debug.php">ملف التشخيص المتقدم</a></li>
        <li><a href="test.php">ملف الاختبار الشامل</a></li>
        <li><a href="index.php">الصفحة الرئيسية للنظام</a></li>
    </ul>
    
    <hr>
    <p><small>إذا استمر الخطأ، تحقق من سجل أخطاء Apache في WAMP</small></p>
</body>
</html>
