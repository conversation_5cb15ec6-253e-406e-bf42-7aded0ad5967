<?php
/**
 * نموذج النشاط الدعوي
 * Activity Model
 */

class Activity {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * إنشاء نشاط جديد
     */
    public function create($data) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO activities (preacher_id, activity_name, execution_date, 
                                      activity_type, attendees_count, notes, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            return $stmt->execute([
                $data['preacher_id'],
                $data['activity_name'],
                $data['execution_date'],
                $data['activity_type'],
                $data['attendees_count'] ?? 0,
                $data['notes'] ?? null,
                $_SESSION['user_id']
            ]);
        } catch (PDOException $e) {
            error_log("خطأ في إنشاء النشاط: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على نشاط بواسطة المعرف
     */
    public function getById($id) {
        try {
            $stmt = $this->db->prepare("
                SELECT a.*, p.full_name as preacher_name, u.full_name as created_by_name
                FROM activities a 
                LEFT JOIN preachers p ON a.preacher_id = p.id 
                LEFT JOIN users u ON a.created_by = u.id 
                WHERE a.id = ?
            ");
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("خطأ في جلب النشاط: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على جميع الأنشطة
     */
    public function getAll($preacherId = null, $limit = null) {
        try {
            $sql = "
                SELECT a.*, p.full_name as preacher_name, u.full_name as created_by_name
                FROM activities a 
                LEFT JOIN preachers p ON a.preacher_id = p.id 
                LEFT JOIN users u ON a.created_by = u.id
            ";
            
            $params = [];
            
            if ($preacherId) {
                $sql .= " WHERE a.preacher_id = ?";
                $params[] = $preacherId;
            }
            
            $sql .= " ORDER BY a.execution_date DESC";
            
            if ($limit) {
                $sql .= " LIMIT ?";
                $params[] = $limit;
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في جلب الأنشطة: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث النشاط
     */
    public function update($id, $data) {
        try {
            $stmt = $this->db->prepare("
                UPDATE activities 
                SET activity_name = ?, execution_date = ?, activity_type = ?, 
                    attendees_count = ?, notes = ?
                WHERE id = ?
            ");
            
            return $stmt->execute([
                $data['activity_name'],
                $data['execution_date'],
                $data['activity_type'],
                $data['attendees_count'] ?? 0,
                $data['notes'] ?? null,
                $id
            ]);
        } catch (PDOException $e) {
            error_log("خطأ في تحديث النشاط: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * حذف النشاط
     */
    public function delete($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM activities WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("خطأ في حذف النشاط: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * البحث في الأنشطة
     */
    public function search($keyword, $preacherId = null) {
        try {
            $sql = "
                SELECT a.*, p.full_name as preacher_name, u.full_name as created_by_name
                FROM activities a 
                LEFT JOIN preachers p ON a.preacher_id = p.id 
                LEFT JOIN users u ON a.created_by = u.id 
                WHERE (a.activity_name LIKE ? OR a.activity_type LIKE ? OR a.notes LIKE ?)
            ";
            
            $params = ["%$keyword%", "%$keyword%", "%$keyword%"];
            
            if ($preacherId) {
                $sql .= " AND a.preacher_id = ?";
                $params[] = $preacherId;
            }
            
            $sql .= " ORDER BY a.execution_date DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في البحث في الأنشطة: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على الأنشطة حسب التاريخ
     */
    public function getByDateRange($startDate, $endDate, $preacherId = null) {
        try {
            $sql = "
                SELECT a.*, p.full_name as preacher_name, u.full_name as created_by_name
                FROM activities a 
                LEFT JOIN preachers p ON a.preacher_id = p.id 
                LEFT JOIN users u ON a.created_by = u.id 
                WHERE a.execution_date BETWEEN ? AND ?
            ";
            
            $params = [$startDate, $endDate];
            
            if ($preacherId) {
                $sql .= " AND a.preacher_id = ?";
                $params[] = $preacherId;
            }
            
            $sql .= " ORDER BY a.execution_date DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في جلب الأنشطة حسب التاريخ: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات الأنشطة
     */
    public function getStats($preacherId = null) {
        try {
            $stats = [];
            
            $whereClause = $preacherId ? "WHERE preacher_id = ?" : "";
            $params = $preacherId ? [$preacherId] : [];
            
            // إجمالي الأنشطة
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM activities $whereClause");
            $stmt->execute($params);
            $stats['total_activities'] = $stmt->fetchColumn();
            
            // إجمالي الحضور
            $stmt = $this->db->prepare("SELECT SUM(attendees_count) FROM activities $whereClause");
            $stmt->execute($params);
            $stats['total_attendees'] = $stmt->fetchColumn() ?? 0;
            
            // الأنشطة حسب النوع
            $stmt = $this->db->prepare("
                SELECT activity_type, COUNT(*) as count 
                FROM activities $whereClause 
                GROUP BY activity_type
            ");
            $stmt->execute($params);
            $stats['by_type'] = $stmt->fetchAll();
            
            // الأنشطة الشهرية
            $stmt = $this->db->prepare("
                SELECT DATE_FORMAT(execution_date, '%Y-%m') as month, COUNT(*) as count 
                FROM activities $whereClause 
                GROUP BY month 
                ORDER BY month DESC 
                LIMIT 12
            ");
            $stmt->execute($params);
            $stats['monthly'] = $stmt->fetchAll();
            
            return $stats;
        } catch (PDOException $e) {
            error_log("خطأ في جلب إحصائيات الأنشطة: " . $e->getMessage());
            return [];
        }
    }
}
?>
