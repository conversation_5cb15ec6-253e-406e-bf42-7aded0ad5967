<?php
/**
 * ملف تشخيص الأخطاء
 * Debug File
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>تشخيص النظام</h1>";

// اختبار PHP
echo "<h2>1. اختبار PHP</h2>";
echo "إصدار PHP: " . PHP_VERSION . "<br>";
echo "امتداد PDO: " . (extension_loaded('pdo') ? 'مثبت ✓' : 'غير مثبت ✗') . "<br>";
echo "امتداد PDO MySQL: " . (extension_loaded('pdo_mysql') ? 'مثبت ✓' : 'غير مثبت ✗') . "<br>";

// اختبار الملفات
echo "<h2>2. اختبار الملفات</h2>";
$files = [
    'config/config.php',
    'config/database.php',
    'includes/functions.php',
    'includes/session.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "$file: موجود ✓<br>";
    } else {
        echo "$file: غير موجود ✗<br>";
    }
}

// اختبار تضمين الملفات
echo "<h2>3. اختبار تضمين الملفات</h2>";

try {
    echo "تضمين config/database.php...<br>";
    require_once 'config/database.php';
    echo "تم بنجاح ✓<br>";
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . " ✗<br>";
}

try {
    echo "تضمين config/config.php...<br>";
    require_once 'config/config.php';
    echo "تم بنجاح ✓<br>";
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . " ✗<br>";
}

// اختبار قاعدة البيانات
echo "<h2>4. اختبار قاعدة البيانات</h2>";
try {
    $db = Database::getInstance()->getConnection();
    echo "الاتصال بقاعدة البيانات: نجح ✓<br>";
    
    // اختبار الجداول
    $tables = ['users', 'preachers', 'activities', 'achievements', 'obstacles'];
    foreach ($tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "جدول $table: موجود ✓<br>";
        } else {
            echo "جدول $table: غير موجود ✗<br>";
        }
    }
} catch (Exception $e) {
    echo "خطأ في قاعدة البيانات: " . $e->getMessage() . " ✗<br>";
}

// اختبار الثوابت
echo "<h2>5. اختبار الثوابت</h2>";
$constants = ['APP_NAME', 'APP_URL', 'ROOT_PATH', 'UPLOAD_PATH'];
foreach ($constants as $constant) {
    if (defined($constant)) {
        echo "$constant: " . constant($constant) . " ✓<br>";
    } else {
        echo "$constant: غير معرف ✗<br>";
    }
}

echo "<h2>6. معلومات إضافية</h2>";
echo "المجلد الحالي: " . getcwd() . "<br>";
echo "مسار الملف: " . __FILE__ . "<br>";
echo "مسار المجلد: " . __DIR__ . "<br>";

echo "<hr>";
echo "<p><strong>إذا رأيت هذه الرسالة، فإن PHP يعمل بشكل صحيح.</strong></p>";
echo "<p>الآن جرب الوصول إلى <a href='index.php'>index.php</a> مرة أخرى.</p>";
?>
