<?php
/**
 * ملف اختبار النظام
 * System Test File
 */

// تضمين ملف الإعدادات
require_once 'config/config.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-card { margin-bottom: 20px; }
        .test-success { color: #28a745; }
        .test-error { color: #dc3545; }
        .test-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            اختبار نظام إدارة شؤون الدعاة
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- اختبار PHP -->
                        <div class="card test-card">
                            <div class="card-header">
                                <h6 class="mb-0">اختبار PHP</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>إصدار PHP:</strong>
                                        <span class="<?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'test-success' : 'test-error'; ?>">
                                            <?php echo PHP_VERSION; ?>
                                            <?php if (version_compare(PHP_VERSION, '7.4.0', '>=')): ?>
                                                <i class="fas fa-check"></i>
                                            <?php else: ?>
                                                <i class="fas fa-times"></i>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>امتداد PDO:</strong>
                                        <span class="<?php echo extension_loaded('pdo') ? 'test-success' : 'test-error'; ?>">
                                            <?php echo extension_loaded('pdo') ? 'مثبت' : 'غير مثبت'; ?>
                                            <i class="fas fa-<?php echo extension_loaded('pdo') ? 'check' : 'times'; ?>"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- اختبار قاعدة البيانات -->
                        <div class="card test-card">
                            <div class="card-header">
                                <h6 class="mb-0">اختبار قاعدة البيانات</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    $db = Database::getInstance()->getConnection();
                                    echo '<div class="alert alert-success">';
                                    echo '<i class="fas fa-check me-2"></i>';
                                    echo 'تم الاتصال بقاعدة البيانات بنجاح';
                                    echo '</div>';
                                    
                                    // اختبار الجداول
                                    $tables = ['users', 'preachers', 'activities', 'achievements', 'obstacles'];
                                    echo '<div class="row">';
                                    foreach ($tables as $table) {
                                        $stmt = $db->query("SHOW TABLES LIKE '$table'");
                                        $exists = $stmt->rowCount() > 0;
                                        echo '<div class="col-md-4 mb-2">';
                                        echo '<span class="' . ($exists ? 'test-success' : 'test-error') . '">';
                                        echo $table . ' ';
                                        echo '<i class="fas fa-' . ($exists ? 'check' : 'times') . '"></i>';
                                        echo '</span>';
                                        echo '</div>';
                                    }
                                    echo '</div>';
                                    
                                } catch (Exception $e) {
                                    echo '<div class="alert alert-danger">';
                                    echo '<i class="fas fa-times me-2"></i>';
                                    echo 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
                                    echo '</div>';
                                }
                                ?>
                            </div>
                        </div>

                        <!-- اختبار الملفات والمجلدات -->
                        <div class="card test-card">
                            <div class="card-header">
                                <h6 class="mb-0">اختبار الملفات والمجلدات</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $paths = [
                                    'config/config.php' => 'ملف الإعدادات',
                                    'config/database.php' => 'ملف قاعدة البيانات',
                                    'public/uploads/' => 'مجلد الرفع',
                                    'public/css/style.css' => 'ملف الأنماط',
                                    'public/js/script.js' => 'ملف JavaScript',
                                    'views/layouts/header.php' => 'ملف الرأس',
                                    'models/User.php' => 'نموذج المستخدم'
                                ];
                                
                                echo '<div class="row">';
                                foreach ($paths as $path => $description) {
                                    $exists = file_exists($path);
                                    $writable = is_writable($path);
                                    
                                    echo '<div class="col-md-6 mb-2">';
                                    echo '<strong>' . $description . ':</strong> ';
                                    echo '<span class="' . ($exists ? 'test-success' : 'test-error') . '">';
                                    echo $exists ? 'موجود' : 'غير موجود';
                                    echo ' <i class="fas fa-' . ($exists ? 'check' : 'times') . '"></i>';
                                    echo '</span>';
                                    
                                    if ($exists && is_dir($path)) {
                                        echo ' <span class="' . ($writable ? 'test-success' : 'test-warning') . '">';
                                        echo '(' . ($writable ? 'قابل للكتابة' : 'للقراءة فقط') . ')';
                                        echo '</span>';
                                    }
                                    echo '</div>';
                                }
                                echo '</div>';
                                ?>
                            </div>
                        </div>

                        <!-- اختبار الإعدادات -->
                        <div class="card test-card">
                            <div class="card-header">
                                <h6 class="mb-0">اختبار الإعدادات</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>اسم التطبيق:</strong> <?php echo APP_NAME; ?>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>إصدار التطبيق:</strong> <?php echo APP_VERSION; ?>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>رابط التطبيق:</strong> <?php echo APP_URL; ?>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>المنطقة الزمنية:</strong> <?php echo date_default_timezone_get(); ?>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>الترميز:</strong> <?php echo ini_get('default_charset'); ?>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>حد رفع الملفات:</strong> <?php echo ini_get('upload_max_filesize'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- اختبار الدوال -->
                        <div class="card test-card">
                            <div class="card-header">
                                <h6 class="mb-0">اختبار الدوال المساعدة</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $functions = [
                                    'sanitize' => 'تنظيف البيانات',
                                    'validateEmail' => 'التحقق من البريد',
                                    'validatePhone' => 'التحقق من الهاتف',
                                    'formatDateArabic' => 'تنسيق التاريخ',
                                    'hashPassword' => 'تشفير كلمة المرور',
                                    'generateToken' => 'إنشاء رمز'
                                ];
                                
                                echo '<div class="row">';
                                foreach ($functions as $function => $description) {
                                    $exists = function_exists($function);
                                    echo '<div class="col-md-4 mb-2">';
                                    echo '<span class="' . ($exists ? 'test-success' : 'test-error') . '">';
                                    echo $description . ' ';
                                    echo '<i class="fas fa-' . ($exists ? 'check' : 'times') . '"></i>';
                                    echo '</span>';
                                    echo '</div>';
                                }
                                echo '</div>';
                                ?>
                            </div>
                        </div>

                        <!-- نتيجة الاختبار -->
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-check-circle me-2"></i>
                                    نتيجة الاختبار
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <h5 class="text-success">
                                    <i class="fas fa-thumbs-up fa-2x mb-3"></i><br>
                                    النظام جاهز للاستخدام!
                                </h5>
                                <p class="text-muted">
                                    جميع المكونات الأساسية تعمل بشكل صحيح
                                </p>
                                <div class="mt-3">
                                    <a href="index.php" class="btn btn-primary btn-lg">
                                        <i class="fas fa-rocket me-2"></i>
                                        بدء استخدام النظام
                                    </a>
                                    <a href="index.php?page=login" class="btn btn-outline-primary btn-lg ms-2">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        تسجيل الدخول
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="mt-4 text-center">
                            <small class="text-muted">
                                <strong>ملاحظة:</strong> احذف هذا الملف (test.php) بعد التأكد من عمل النظام
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
