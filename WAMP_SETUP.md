# إعداد النظام على WAMP Server

## المشاكل الشائعة وحلولها

### 1. مشكلة ملف .htaccess
إذا لم يعمل ملف `.htaccess`، جرب:

#### الحل الأول: تفعيل mod_rewrite
1. افتح WAMP
2. اضغط على أيقونة WAMP في شريط المهام
3. اختر Apache → Apache Modules
4. تأكد من تفعيل `rewrite_module`

#### الحل الثاني: تعديل httpd.conf
1. WAMP → Apache → httpd.conf
2. ابحث عن: `#LoadModule rewrite_module modules/mod_rewrite.so`
3. احذف علامة `#` من بداية السطر
4. ابحث عن: `AllowOverride None`
5. غيرها إلى: `AllowOverride All`
6. أعد تشغيل Apache

#### الحل الثالث: إذا استمرت المشكلة
- غير اسم `.htaccess` إلى `.htaccess.txt`
- أو احذف الملف نهائياً واستخدم النظام بدون URL rewriting

### 2. مشكلة قاعدة البيانات
إذا لم تعمل قاعدة البيانات:

1. تأكد من تشغيل MySQL (أيقونة خضراء)
2. افتح phpMyAdmin: http://localhost/phpmyadmin
3. أنشئ قاعدة بيانات جديدة: `dawah_management`
4. استورد ملف `sql/database.sql`

### 3. مشكلة الترميز العربي
إذا ظهرت الأحرف العربية بشكل خاطئ:

1. تأكد من ترميز UTF-8 في المتصفح
2. تأكد من ترميز قاعدة البيانات: `utf8mb4_unicode_ci`

### 4. مشكلة الصلاحيات
إذا ظهرت أخطاء في الصلاحيات:

1. تأكد من صلاحيات مجلد `uploads/`
2. في Windows: Properties → Security → Edit
3. أعط صلاحيات كاملة لـ `Everyone`

## روابط مفيدة للاختبار

- الصفحة الرئيسية: http://localhost/da/
- اختبار PHP: http://localhost/da/hello.php
- اختبار قاعدة البيانات: http://localhost/da/test_db.php
- إنشاء قاعدة البيانات: http://localhost/da/create_database.php
- phpMyAdmin: http://localhost/phpmyadmin

## معلومات النظام

- **اسم قاعدة البيانات**: dawah_management
- **المستخدم**: root
- **كلمة المرور**: (فارغة)
- **الخادم**: localhost
- **المنفذ**: 3306

## ملاحظات مهمة

1. **للتطوير فقط**: عرض الأخطاء مفعل
2. **للإنتاج**: يجب تعطيل عرض الأخطاء
3. **الأمان**: يجب تغيير كلمات المرور الافتراضية
4. **النسخ الاحتياطي**: احفظ نسخة من قاعدة البيانات دورياً
