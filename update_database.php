<?php
/**
 * تحديث قاعدة البيانات لإضافة الحقول الجديدة
 * Database Update Script
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once 'config/config.php';

echo "<h1>🔄 تحديث قاعدة البيانات</h1>";

try {
    $db = Database::getInstance()->getConnection();
    
    echo "<h2>1. فحص جدول preachers:</h2>";
    
    // فحص الأعمدة الموجودة
    $stmt = $db->prepare("DESCRIBE preachers");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p>الأعمدة الموجودة حالياً:</p>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    
    // الأعمدة المطلوبة الجديدة
    $newColumns = [
        'phone' => "VARCHAR(20) DEFAULT ''",
        'address' => "TEXT DEFAULT ''",
        'education' => "VARCHAR(255) DEFAULT ''",
        'experience_years' => "INT DEFAULT 0",
        'languages' => "VARCHAR(255) DEFAULT ''",
        'bio' => "TEXT DEFAULT ''"
    ];
    
    echo "<h2>2. إضافة الأعمدة الجديدة:</h2>";
    
    foreach ($newColumns as $columnName => $columnDefinition) {
        if (!in_array($columnName, $columns)) {
            try {
                $sql = "ALTER TABLE preachers ADD COLUMN $columnName $columnDefinition";
                $db->exec($sql);
                echo "<p style='color: green;'>✓ تم إضافة العمود: $columnName</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في إضافة العمود $columnName: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ العمود $columnName موجود مسبقاً</p>";
        }
    }
    
    // إزالة الأعمدة القديمة إذا كانت موجودة
    $oldColumns = ['nationality', 'language', 'mobile_phone', 'notes'];
    
    echo "<h2>3. تنظيف الأعمدة القديمة:</h2>";
    
    foreach ($oldColumns as $oldColumn) {
        if (in_array($oldColumn, $columns)) {
            try {
                // نسخ البيانات إلى الأعمدة الجديدة إذا أمكن
                if ($oldColumn === 'mobile_phone' && in_array('phone', $columns)) {
                    $db->exec("UPDATE preachers SET phone = mobile_phone WHERE phone = '' AND mobile_phone IS NOT NULL");
                    echo "<p style='color: blue;'>ℹ️ تم نسخ البيانات من mobile_phone إلى phone</p>";
                }
                
                if ($oldColumn === 'notes' && in_array('bio', $columns)) {
                    $db->exec("UPDATE preachers SET bio = notes WHERE bio = '' AND notes IS NOT NULL");
                    echo "<p style='color: blue;'>ℹ️ تم نسخ البيانات من notes إلى bio</p>";
                }
                
                // حذف العمود القديم
                $sql = "ALTER TABLE preachers DROP COLUMN $oldColumn";
                $db->exec($sql);
                echo "<p style='color: orange;'>🗑️ تم حذف العمود القديم: $oldColumn</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ خطأ في حذف العمود $oldColumn: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h2>4. فحص النتيجة النهائية:</h2>";
    
    // فحص الأعمدة بعد التحديث
    $stmt = $db->prepare("DESCRIBE preachers");
    $stmt->execute();
    $finalColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p>الأعمدة بعد التحديث:</p>";
    echo "<ul>";
    foreach ($finalColumns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    
    echo "<h2>✅ تم تحديث قاعدة البيانات بنجاح!</h2>";
    
    echo "<h2>🔗 الروابط:</h2>";
    echo "<ul>";
    echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
    echo "<li><a href='index.php?page=users'>إدارة المستخدمين</a></li>";
    echo "<li><a href='index.php?page=preachers'>إدارة الدعاة</a></li>";
    echo "<li><a href='quick_test.php'>اختبار سريع</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
