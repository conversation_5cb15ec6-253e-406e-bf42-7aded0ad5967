/* 
 * ملف الأنماط المخصص لتطبيق إدارة شؤون الدعاة
 * Custom CSS for Da'wah Management System
 */

/* إعدادات عامة */
:root {
    --primary-color: #2c5530;
    --secondary-color: #3d7c47;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* الخط العربي */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* تحسينات الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    border: none;
    padding: 10px 20px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1e3a21 0%, #2d5a35 100%);
}

/* تحسينات البطاقات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
}

/* تحسينات النماذج */
.form-control,
.form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: var(--transition);
    font-size: 14px;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* تحسينات الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.table td {
    padding: 15px;
    vertical-align: middle;
    border-color: #e9ecef;
}

/* تحسينات الشريط الجانبي */
.sidebar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 15px 20px;
    margin: 3px 0;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: #fff;
    background-color: rgba(255,255,255,0.15);
    transform: translateX(-5px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
}

.sidebar .nav-link i {
    margin-left: 12px;
    width: 20px;
    text-align: center;
}

/* تحسينات الشارات */
.badge {
    font-size: 0.8em;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 600;
}

/* تحسينات التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    box-shadow: var(--box-shadow);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-right: 4px solid var(--primary-color);
    transition: var(--transition);
}

.stats-card:hover {
    border-right-width: 8px;
    transform: translateY(-5px);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* رأس الصفحة */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: var(--box-shadow);
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    color: white;
}

.breadcrumb-item.active {
    color: white;
}

/* المحتوى الرئيسي */
.main-content {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    margin: 20px;
    padding: 30px;
    min-height: calc(100vh - 200px);
}

/* شريط التنقل */
.navbar {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    background: white !important;
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
    font-size: 1.5rem;
}

/* تحسينات الأيقونات */
.fa-mosque {
    color: var(--primary-color);
    filter: drop-shadow(0 2px 4px rgba(44, 85, 48, 0.3));
}

/* تحسينات الصور */
.avatar-sm {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 16px;
}

/* تحسينات النصوص */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* تحسينات الروابط */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

/* تحسينات مجموعات الأزرار */
.btn-group .btn {
    margin: 0 1px;
    border-radius: var(--border-radius) !important;
}

/* تحسينات القوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
}

.dropdown-item {
    transition: var(--transition);
    padding: 10px 20px;
}

.dropdown-item:hover {
    background-color: var(--light-color);
    transform: translateX(5px);
}

/* تحسينات الفواصل */
hr {
    border-color: rgba(255,255,255,0.2);
    margin: 20px 0;
}

/* تحسينات التذييل */
footer {
    background: linear-gradient(135deg, var(--light-color) 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    margin-top: 50px;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .main-content {
        margin: 10px;
        padding: 20px;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .page-header {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .sidebar {
        position: fixed;
        top: 0;
        right: -100%;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: var(--transition);
    }
    
    .sidebar.show {
        right: 0;
    }
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .page-header .col-auto {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        padding: 0;
        box-shadow: none;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}

/* تحسينات إضافية */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* تحسينات الأداء */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

.img-thumbnail {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}
