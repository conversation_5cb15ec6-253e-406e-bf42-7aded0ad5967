<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? APP_NAME; ?></title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo APP_URL; ?>/public/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: #2c5530 !important;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #2c5530 0%, #3d7c47 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #2c5530 0%, #3d7c47 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #1e3a21 0%, #2d5a35 100%);
            transform: translateY(-2px);
        }
        
        .alert {
            border: none;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .table {
            border-radius: 8px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #2c5530 0%, #3d7c47 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .badge {
            font-size: 0.8em;
            padding: 6px 12px;
            border-radius: 20px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-right: 4px solid #2c5530;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c5530;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #2c5530;
            box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
        }
        
        .page-header {
            background: linear-gradient(135deg, #2c5530 0%, #3d7c47 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .breadcrumb {
            background: transparent;
            padding: 0;
        }
        
        .breadcrumb-item a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
        }
        
        .breadcrumb-item.active {
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?php echo APP_URL; ?>/index.php">
                <i class="fas fa-mosque me-2"></i>
                <?php echo APP_NAME; ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <?php if (isLoggedIn()): ?>
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo $_SESSION['full_name']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="<?php echo APP_URL; ?>/index.php?page=change_password">
                                    <i class="fas fa-key me-2"></i>
                                    تغيير كلمة المرور
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="<?php echo APP_URL; ?>/index.php?page=logout">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <?php if (isLoggedIn()): ?>
                <!-- Sidebar -->
                <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                    <div class="position-sticky pt-3">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link <?php echo ($page ?? '') === 'dashboard' ? 'active' : ''; ?>" 
                                   href="<?php echo APP_URL; ?>/index.php?page=dashboard">
                                    <i class="fas fa-tachometer-alt"></i>
                                    لوحة التحكم
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a class="nav-link <?php echo ($page ?? '') === 'preachers' ? 'active' : ''; ?>" 
                                   href="<?php echo APP_URL; ?>/index.php?page=preachers">
                                    <i class="fas fa-users"></i>
                                    الدعاة
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a class="nav-link <?php echo ($page ?? '') === 'activities' ? 'active' : ''; ?>" 
                                   href="<?php echo APP_URL; ?>/index.php?page=activities">
                                    <i class="fas fa-calendar-alt"></i>
                                    الأنشطة الدعوية
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a class="nav-link <?php echo ($page ?? '') === 'achievements' ? 'active' : ''; ?>" 
                                   href="<?php echo APP_URL; ?>/index.php?page=achievements">
                                    <i class="fas fa-trophy"></i>
                                    الإنجازات
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a class="nav-link <?php echo ($page ?? '') === 'obstacles' ? 'active' : ''; ?>" 
                                   href="<?php echo APP_URL; ?>/index.php?page=obstacles">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    المعرقلات
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/index.php?page=dashboard&action=reports">
                                    <i class="fas fa-chart-bar"></i>
                                    التقارير
                                </a>
                            </li>
                            
                            <?php if (isAdmin()): ?>
                                <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo APP_URL; ?>/index.php?page=register">
                                        <i class="fas fa-user-plus"></i>
                                        إضافة مستخدم
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </nav>
                
                <!-- Main content -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <?php else: ?>
                <!-- Full width for login page -->
                <main class="col-12">
            <?php endif; ?>
            
            <!-- Display messages -->
            <?php 
            $message = getMessage();
            if ($message): 
            ?>
                <div class="alert alert-<?php echo $message['type'] === 'error' ? 'danger' : $message['type']; ?> alert-dismissible fade show mt-3" role="alert">
                    <?php echo $message['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
