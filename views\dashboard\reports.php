<?php
/**
 * صفحة التقارير الإحصائية
 * Reports Page
 */

requireLogin();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير الإحصائية - <?php echo APP_NAME; ?></title>
    <link href="<?php echo APP_URL; ?>/assets/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="<?php echo APP_URL; ?>/assets/css/style.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <?php include ROOT_PATH . '/views/layouts/navbar.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include ROOT_PATH . '/views/layouts/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">التقارير الإحصائية</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="<?php echo APP_URL; ?>/index.php?page=dashboard" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> العودة للوحة التحكم
                        </a>
                    </div>
                </div>

                <!-- إحصائيات عامة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center bg-primary text-white">
                            <div class="card-body">
                                <i class="bi bi-people" style="font-size: 2rem;"></i>
                                <h4 class="mt-2"><?php echo $stats['total_preachers'] ?? 0; ?></h4>
                                <p class="mb-0">إجمالي الدعاة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-success text-white">
                            <div class="card-body">
                                <i class="bi bi-calendar-event" style="font-size: 2rem;"></i>
                                <h4 class="mt-2"><?php echo $stats['total_activities'] ?? 0; ?></h4>
                                <p class="mb-0">إجمالي الأنشطة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-warning text-white">
                            <div class="card-body">
                                <i class="bi bi-trophy" style="font-size: 2rem;"></i>
                                <h4 class="mt-2"><?php echo $stats['total_achievements'] ?? 0; ?></h4>
                                <p class="mb-0">إجمالي الإنجازات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-danger text-white">
                            <div class="card-body">
                                <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                                <h4 class="mt-2"><?php echo $stats['total_obstacles'] ?? 0; ?></h4>
                                <p class="mb-0">إجمالي المعرقلات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">توزيع الدعاة حسب التخصص</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="specializationChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">الأنشطة الشهرية</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="monthlyActivitiesChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">الدعاة الأكثر نشاطاً</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($topPreachers)): ?>
                                    <p class="text-muted text-center">لا توجد بيانات متاحة</p>
                                <?php else: ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($topPreachers as $index => $preacher): ?>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong><?php echo htmlspecialchars($preacher['full_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($preacher['specialization']); ?></small>
                                                </div>
                                                <span class="badge bg-primary rounded-pill">
                                                    <?php echo $preacher['activities_count']; ?> نشاط
                                                </span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">إحصائيات سريعة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6 mb-3">
                                        <h4 class="text-primary"><?php echo $stats['active_preachers'] ?? 0; ?></h4>
                                        <small class="text-muted">دعاة نشطين</small>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <h4 class="text-success"><?php echo $stats['this_month_activities'] ?? 0; ?></h4>
                                        <small class="text-muted">أنشطة هذا الشهر</small>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <h4 class="text-warning"><?php echo $stats['total_attendees'] ?? 0; ?></h4>
                                        <small class="text-muted">إجمالي الحضور</small>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <h4 class="text-info"><?php echo $stats['avg_experience'] ?? 0; ?></h4>
                                        <small class="text-muted">متوسط سنوات الخبرة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار التصدير -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">تصدير التقارير</h5>
                            </div>
                            <div class="card-body">
                                <div class="btn-group" role="group">
                                    <a href="<?php echo APP_URL; ?>/export_data.php?type=preachers"
                                       class="btn btn-outline-primary">
                                        <i class="bi bi-download"></i> تصدير بيانات الدعاة
                                    </a>
                                    <?php if (isAdmin()): ?>
                                        <a href="<?php echo APP_URL; ?>/export_data.php?type=users"
                                           class="btn btn-outline-warning">
                                            <i class="bi bi-download"></i> تصدير المستخدمين
                                        </a>
                                    <?php endif; ?>
                                    <a href="<?php echo APP_URL; ?>/export_data.php?type=full"
                                       class="btn btn-outline-info">
                                        <i class="bi bi-download"></i> تصدير تقرير شامل
                                    </a>
                                </div>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        سيتم تصدير البيانات بصيغة CSV يمكن فتحها في Excel
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="<?php echo APP_URL; ?>/assets/js/bootstrap.bundle.min.js"></script>
    <script>
        // رسم بياني للتخصصات
        const specializationCtx = document.getElementById('specializationChart').getContext('2d');
        new Chart(specializationCtx, {
            type: 'doughnut',
            data: {
                labels: <?php echo json_encode(array_keys($specializationData ?? [])); ?>,
                datasets: [{
                    data: <?php echo json_encode(array_values($specializationData ?? [])); ?>,
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // رسم بياني للأنشطة الشهرية
        const monthlyCtx = document.getElementById('monthlyActivitiesChart').getContext('2d');
        new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode(array_keys($monthlyData ?? [])); ?>,
                datasets: [{
                    label: 'عدد الأنشطة',
                    data: <?php echo json_encode(array_values($monthlyData ?? [])); ?>,
                    borderColor: '#36A2EB',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
