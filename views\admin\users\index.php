<?php
/**
 * صفحة قائمة المستخدمين
 * Users List Page
 */

requireAdmin();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - <?php echo APP_NAME; ?></title>
    <link href="<?php echo APP_URL; ?>/assets/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="<?php echo APP_URL; ?>/assets/css/style.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <?php include ROOT_PATH . '/views/layouts/navbar.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include ROOT_PATH . '/views/layouts/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة المستخدمين</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="<?php echo APP_URL; ?>/index.php?page=users&action=create" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> إضافة مستخدم جديد
                        </a>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                        <?php
                        echo $_SESSION['message'];
                        unset($_SESSION['message'], $_SESSION['message_type']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">قائمة المستخدمين</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                            <div class="text-center py-4">
                                <i class="bi bi-people" style="font-size: 3rem; color: #6c757d;"></i>
                                <p class="mt-2 text-muted">لا توجد مستخدمين مسجلين</p>
                                <a href="<?php echo APP_URL; ?>/index.php?page=users&action=create" class="btn btn-primary">
                                    إضافة أول مستخدم
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>المعرف</th>
                                            <th>اسم المستخدم</th>
                                            <th>الاسم الكامل</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الدور</th>
                                            <th>الحالة</th>
                                            <th>تاريخ التسجيل</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($users as $user): ?>
                                            <tr>
                                                <td><?php echo $user['id']; ?></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                                    <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                                        <span class="badge bg-info">أنت</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                <td>
                                                    <?php if ($user['role'] === 'admin'): ?>
                                                        <span class="badge bg-danger">مشرف</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-primary">داعية</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($user['is_active']): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">معطل</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="<?php echo APP_URL; ?>/index.php?page=users&action=edit&id=<?php echo $user['id']; ?>"
                                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>

                                                        <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                            <a href="<?php echo APP_URL; ?>/index.php?page=users&action=toggle&id=<?php echo $user['id']; ?>"
                                                               class="btn btn-sm btn-outline-warning"
                                                               title="<?php echo $user['is_active'] ? 'تعطيل' : 'تفعيل'; ?>"
                                                               onclick="return confirm('هل أنت متأكد؟')">
                                                                <i class="bi bi-<?php echo $user['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                            </a>

                                                            <a href="<?php echo APP_URL; ?>/index.php?page=users&action=delete&id=<?php echo $user['id']; ?>"
                                                               class="btn btn-sm btn-outline-danger" title="حذف"
                                                               onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                                <i class="bi bi-trash"></i>
                                                            </a>
                                                        <?php endif; ?>

                                                        <?php if ($user['role'] === 'preacher'): ?>
                                                            <?php if (isset($user['preacher_info']) && $user['preacher_info']): ?>
                                                                <a href="<?php echo APP_URL; ?>/index.php?page=preachers&action=view&id=<?php echo $user['preacher_info']['id']; ?>"
                                                                   class="btn btn-sm btn-outline-info" title="عرض ملف الداعية">
                                                                    <i class="bi bi-person-badge"></i>
                                                                </a>
                                                            <?php else: ?>
                                                                <span class="badge bg-warning">لم يتم ربط ملف الداعية</span>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">إجمالي المستخدمين</h5>
                                <h2 class="text-primary"><?php echo count($users); ?></h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">المشرفين</h5>
                                <h2 class="text-danger">
                                    <?php echo count(array_filter($users, function($u) { return $u['role'] === 'admin'; })); ?>
                                </h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">الدعاة</h5>
                                <h2 class="text-primary">
                                    <?php echo count(array_filter($users, function($u) { return $u['role'] === 'preacher'; })); ?>
                                </h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">المستخدمين النشطين</h5>
                                <h2 class="text-success">
                                    <?php echo count(array_filter($users, function($u) { return $u['is_active']; })); ?>
                                </h2>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="<?php echo APP_URL; ?>/assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>
