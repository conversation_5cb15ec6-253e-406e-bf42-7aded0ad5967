<?php
$pageTitle = 'الدعاة - ' . APP_NAME;
include ROOT_PATH . '/views/layouts/header.php';
?>

<div class="main-content">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-users me-2"></i>
                    إدارة الدعاة
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="<?php echo APP_URL; ?>/index.php?page=dashboard">الرئيسية</a>
                        </li>
                        <li class="breadcrumb-item active">الدعاة</li>
                    </ol>
                </nav>
            </div>
            <div class="col-auto">
                <?php if (isAdmin()): ?>
                    <a href="<?php echo APP_URL; ?>/index.php?page=preachers&action=create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة داعية جديد
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo APP_URL; ?>/index.php">
                <input type="hidden" name="page" value="preachers">
                <div class="row align-items-end">
                    <div class="col-md-6">
                        <label for="search" class="form-label">البحث</label>
                        <div class="input-group">
                            <input type="text" 
                                   class="form-control" 
                                   id="search" 
                                   name="search" 
                                   value="<?php echo htmlspecialchars($data['search']); ?>"
                                   placeholder="البحث في الاسم، الجنسية، البريد، أو التخصص...">
                            <button class="btn btn-outline-secondary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            بحث
                        </button>
                        <?php if (!empty($data['search'])): ?>
                            <a href="<?php echo APP_URL; ?>/index.php?page=preachers" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-3 text-end">
                        <small class="text-muted">
                            إجمالي النتائج: <?php echo count($data['preachers']); ?>
                        </small>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Preachers List -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-list me-2"></i>
                قائمة الدعاة
                <?php if (!empty($data['search'])): ?>
                    - نتائج البحث عن: "<?php echo htmlspecialchars($data['search']); ?>"
                <?php endif; ?>
            </h6>
        </div>
        <div class="card-body">
            <?php if (!empty($data['preachers'])): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الاسم الكامل</th>
                                <th>الجنسية</th>
                                <th>اللغة</th>
                                <th>رقم الجوال</th>
                                <th>البريد الإلكتروني</th>
                                <th>التخصص</th>
                                <th>الحالة</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data['preachers'] as $preacher): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0"><?php echo htmlspecialchars($preacher['full_name']); ?></h6>
                                                <?php if ($preacher['username']): ?>
                                                    <small class="text-muted">
                                                        <i class="fas fa-user-tag me-1"></i>
                                                        <?php echo htmlspecialchars($preacher['username']); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            <?php echo htmlspecialchars($preacher['nationality']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($preacher['language']); ?></td>
                                    <td>
                                        <?php if ($preacher['mobile_phone']): ?>
                                            <a href="tel:<?php echo $preacher['mobile_phone']; ?>" class="text-decoration-none">
                                                <i class="fas fa-phone me-1"></i>
                                                <?php echo htmlspecialchars($preacher['mobile_phone']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($preacher['email']): ?>
                                            <a href="mailto:<?php echo $preacher['email']; ?>" class="text-decoration-none">
                                                <i class="fas fa-envelope me-1"></i>
                                                <?php echo htmlspecialchars($preacher['email']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($preacher['specialization']): ?>
                                            <span class="badge bg-info">
                                                <?php echo truncateText($preacher['specialization'], 30); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $preacher['is_active'] ? 'success' : 'danger'; ?>">
                                            <?php echo $preacher['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo formatDateArabic($preacher['created_at']); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <!-- View Button -->
                                            <a href="<?php echo APP_URL; ?>/index.php?page=preachers&action=show&id=<?php echo $preacher['id']; ?>" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <!-- Edit Button -->
                                            <?php if (isAdmin() || checkOwnership($preacher['id'])): ?>
                                                <a href="<?php echo APP_URL; ?>/index.php?page=preachers&action=edit&id=<?php echo $preacher['id']; ?>" 
                                                   class="btn btn-sm btn-outline-warning" 
                                                   title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            <?php endif; ?>
                                            
                                            <!-- Admin Only Actions -->
                                            <?php if (isAdmin()): ?>
                                                <!-- Toggle Active Button -->
                                                <form method="POST" 
                                                      action="<?php echo APP_URL; ?>/index.php?page=preachers&action=toggle_active&id=<?php echo $preacher['id']; ?>" 
                                                      class="d-inline"
                                                      onsubmit="return confirmToggle('هل أنت متأكد من تغيير حالة الداعية؟')">
                                                    <input type="hidden" name="csrf_token" value="<?php echo getCSRFToken(); ?>">
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-<?php echo $preacher['is_active'] ? 'secondary' : 'success'; ?>" 
                                                            title="<?php echo $preacher['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                        <i class="fas fa-<?php echo $preacher['is_active'] ? 'ban' : 'check'; ?>"></i>
                                                    </button>
                                                </form>
                                                
                                                <!-- Delete Button -->
                                                <form method="POST" 
                                                      action="<?php echo APP_URL; ?>/index.php?page=preachers&action=delete&id=<?php echo $preacher['id']; ?>" 
                                                      class="d-inline"
                                                      onsubmit="return confirmDelete('هل أنت متأكد من حذف هذا الداعية؟ سيتم حذف جميع بياناته المرتبطة.')">
                                                    <input type="hidden" name="csrf_token" value="<?php echo getCSRFToken(); ?>">
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">
                        <?php if (!empty($data['search'])): ?>
                            لا توجد نتائج للبحث عن "<?php echo htmlspecialchars($data['search']); ?>"
                        <?php else: ?>
                            لا يوجد دعاة مسجلين حالياً
                        <?php endif; ?>
                    </h5>
                    <p class="text-muted">
                        <?php if (isAdmin()): ?>
                            يمكنك إضافة داعية جديد من خلال النقر على زر "إضافة داعية جديد" أعلاه.
                        <?php else: ?>
                            يرجى التواصل مع المشرف لإضافة الدعاة.
                        <?php endif; ?>
                    </p>
                    <?php if (isAdmin()): ?>
                        <a href="<?php echo APP_URL; ?>/index.php?page=preachers&action=create" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة داعية جديد
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
    }
    
    .table td {
        vertical-align: middle;
    }
    
    .btn-group .btn {
        margin: 0 1px;
    }
    
    .badge {
        font-size: 0.75em;
    }
</style>

<?php include ROOT_PATH . '/views/layouts/footer.php'; ?>
